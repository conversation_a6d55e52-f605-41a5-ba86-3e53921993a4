/**
 * Enhanced Agent System
 *
 * This module integrates all components of the enhanced response system to provide
 * comprehensive, intelligent, and contextually relevant agent responses.
 */

import { ResponseEngine, EnhancedResponse, InputAnalysis } from './responseEngine'
import { IntelligenceLayer, AdvancedAnalysis } from './intelligenceLayer'
import { ResponseTemplateEngine, TemplateContext } from './responseTemplates'
import { AgentKnowledgeBase } from './knowledgeBase'

export interface AgentResponse {
  id: string
  agentId: string
  input: string
  analysis: AdvancedAnalysis
  response: EnhancedResponse
  formattedOutput: string
  metadata: ResponseMetadata
  timestamp: Date
}

export interface ResponseMetadata {
  processingTime: number
  confidence: number
  completeness: number
  qualityScore: number
  templateUsed: string
  riskFactors: number
  opportunities: number
  followUpRecommended: boolean
}

export interface AgentConfig {
  id: string
  name: string
  specialization: string[]
  responseStyle: 'concise' | 'detailed' | 'comprehensive'
  minQualityThreshold: number
  enableAdvancedAnalysis: boolean
}

export class EnhancedAgentSystem {
  private static instance: EnhancedAgentSystem
  private responseEngine: ResponseEngine
  private intelligenceLayer: IntelligenceLayer
  private templateEngine: ResponseTemplateEngine
  private knowledgeBase: AgentKnowledgeBase
  private agentConfigs: Map<string, AgentConfig> = new Map()

  private constructor() {
    this.responseEngine = ResponseEngine.getInstance()
    this.intelligenceLayer = IntelligenceLayer.getInstance()
    this.templateEngine = ResponseTemplateEngine.getInstance()
    this.knowledgeBase = AgentKnowledgeBase.getInstance()
    this.initializeAgentConfigs()
  }

  public static getInstance(): EnhancedAgentSystem {
    if (!EnhancedAgentSystem.instance) {
      EnhancedAgentSystem.instance = new EnhancedAgentSystem()
    }
    return EnhancedAgentSystem.instance
  }

  /**
   * Main method to generate enhanced agent responses
   */
  public async generateResponse(agentId: string, input: string, userContext?: Record<string, any>): Promise<AgentResponse> {
    const startTime = Date.now()
    const config = this.agentConfigs.get(agentId)

    if (!config) {
      throw new Error(`Agent configuration not found for: ${agentId}`)
    }

    try {
      // Step 1: Perform advanced input analysis
      const analysis = config.enableAdvancedAnalysis
        ? this.intelligenceLayer.performAdvancedAnalysis(input)
        : this.performBasicAnalysis(input)

      // Step 2: Generate template-based response
      const templateContext: TemplateContext = {
        agentId,
        analysis,
        input,
        userContext
      }

      const templateSections = this.templateEngine.generateResponse(templateContext)

      // Step 3: Generate enhanced response using response engine
      const enhancedResponse = this.responseEngine.generateResponse(agentId, input, analysis)

      // Step 4: Merge template sections with enhanced response
      const mergedSections = this.mergeSections(templateSections, enhancedResponse.sections)

      // Step 5: Apply agent-specific enhancements
      const finalResponse: EnhancedResponse = {
        ...enhancedResponse,
        sections: mergedSections,
        confidence: this.calculateFinalConfidence(analysis, mergedSections),
        completeness: this.calculateFinalCompleteness(analysis, mergedSections)
      }

      // Step 6: Format output based on agent style
      const formattedOutput = this.formatResponse(finalResponse, config.responseStyle)

      // Step 7: Calculate metadata
      const processingTime = Date.now() - startTime
      const metadata = this.calculateMetadata(analysis, finalResponse, processingTime, config)

      // Step 8: Quality validation
      if (metadata.qualityScore < config.minQualityThreshold) {
        console.warn(`Response quality below threshold for agent ${agentId}: ${metadata.qualityScore}`)
        // Could trigger fallback or enhancement here
      }

      return {
        id: this.generateResponseId(),
        agentId,
        input,
        analysis,
        response: finalResponse,
        formattedOutput,
        metadata,
        timestamp: new Date()
      }

    } catch (error) {
      console.error(`Error generating response for agent ${agentId}:`, error)
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Batch processing for multiple inputs
   */
  public async generateBatchResponses(
    agentId: string,
    inputs: string[],
    userContext?: Record<string, any>
  ): Promise<AgentResponse[]> {
    const responses: AgentResponse[] = []

    for (const input of inputs) {
      try {
        const response = await this.generateResponse(agentId, input, userContext)
        responses.push(response)
      } catch (error) {
        console.error(`Failed to process input: ${input}`, error)
        // Continue with other inputs
      }
    }

    return responses
  }

  /**
   * Get agent capabilities and specializations
   */
  public getAgentCapabilities(agentId: string): {
    specializations: string[]
    knowledgeAreas: string[]
    responseStyle: string
    qualityThreshold: number
  } | null {
    const config = this.agentConfigs.get(agentId)
    const knowledge = this.knowledgeBase.getAgentKnowledge(agentId)

    if (!config || !knowledge) return null

    return {
      specializations: config.specialization,
      knowledgeAreas: knowledge.specializations,
      responseStyle: config.responseStyle,
      qualityThreshold: config.minQualityThreshold
    }
  }

  /**
   * Analyze input without generating full response (for preview/validation)
   */
  public analyzeInput(input: string): AdvancedAnalysis {
    return this.intelligenceLayer.performAdvancedAnalysis(input)
  }

  private initializeAgentConfigs(): void {
    this.agentConfigs.set('atendimento', {
      id: 'atendimento',
      name: 'Agente de Atendimento',
      specialization: ['briefing', 'client-communication', 'project-management'],
      responseStyle: 'comprehensive',
      minQualityThreshold: 0.8,
      enableAdvancedAnalysis: true
    })

    this.agentConfigs.set('planejamento', {
      id: 'planejamento',
      name: 'Agente de Planejamento',
      specialization: ['strategy', 'planning', 'analysis'],
      responseStyle: 'detailed',
      minQualityThreshold: 0.85,
      enableAdvancedAnalysis: true
    })

    this.agentConfigs.set('midia', {
      id: 'midia',
      name: 'Agente de Mídia',
      specialization: ['performance', 'optimization', 'analytics'],
      responseStyle: 'detailed',
      minQualityThreshold: 0.8,
      enableAdvancedAnalysis: true
    })
  }

  private performBasicAnalysis(input: string): AdvancedAnalysis {
    // Fallback to basic analysis if advanced is disabled
    const basicAnalysis = this.responseEngine.analyzeInput(input)

    // Convert to AdvancedAnalysis format with minimal additional data
    return {
      ...basicAnalysis,
      namedEntities: [],
      topicClusters: [],
      linguisticFeatures: {
        readabilityScore: 50,
        formalityLevel: 'neutral',
        technicalComplexity: 0.1,
        emotionalIntensity: 0.1,
        questionCount: (input.match(/\?/g) || []).length,
        imperativeCount: 0,
        averageSentenceLength: input.split(/[.!?]+/).length > 0 ?
          input.split(/\s+/).length / input.split(/[.!?]+/).length : 0
      },
      businessContext: {
        industry: [],
        businessStage: 'growth',
        marketingMaturity: 'intermediate',
        budgetRange: 'small',
        timeframe: 'medium-term'
      },
      riskFactors: [],
      opportunityIndicators: []
    }
  }

  private mergeSections(templateSections: any[], enhancedSections: any[]): any[] {
    console.log('Template sections:', templateSections.length)
    console.log('Enhanced sections:', enhancedSections.length)

    // If we have template sections, use them as primary source
    if (templateSections.length > 0) {
      console.log('Using template sections as primary')
      return templateSections.sort((a, b) => (b.priority || 0) - (a.priority || 0))
    }

    // Fallback to enhanced sections if no templates
    console.log('Falling back to enhanced sections')
    return enhancedSections.sort((a, b) => (b.priority || 0) - (a.priority || 0))
  }

  private calculateFinalConfidence(analysis: AdvancedAnalysis, sections: any[]): number {
    let confidence = analysis.completeness * 0.4 // Base on input completeness

    // Boost confidence based on number of quality sections
    confidence += Math.min(sections.length * 0.1, 0.4)

    // Adjust based on risk factors
    if (analysis.riskFactors.length > 0) {
      const avgRiskSeverity = analysis.riskFactors.reduce((sum, risk) => {
        const severityScore = { low: 0.1, medium: 0.3, high: 0.5, critical: 0.7 }
        return sum + (severityScore[risk.severity] || 0.3)
      }, 0) / analysis.riskFactors.length

      confidence -= avgRiskSeverity * 0.2
    }

    return Math.max(0.1, Math.min(1.0, confidence))
  }

  private calculateFinalCompleteness(analysis: AdvancedAnalysis, sections: any[]): number {
    let completeness = analysis.completeness * 0.5 // Base on input completeness

    // Add completeness based on section coverage
    const requiredSectionTypes = ['analysis', 'recommendation', 'next-steps']
    const presentTypes = new Set(sections.map(s => s.type))
    const typesCovered = requiredSectionTypes.filter(type => presentTypes.has(type)).length

    completeness += (typesCovered / requiredSectionTypes.length) * 0.5

    return Math.min(1.0, completeness)
  }

  private formatResponse(response: EnhancedResponse, style: 'concise' | 'detailed' | 'comprehensive'): string {
    let formatted = `# ${response.summary}\n\n`

    // Filter sections based on style
    let sectionsToInclude = response.sections

    if (style === 'concise') {
      sectionsToInclude = response.sections
        .filter(s => s.type === 'analysis' || s.type === 'recommendation' || s.type === 'next-steps')
        .slice(0, 3)
    } else if (style === 'detailed') {
      sectionsToInclude = response.sections.slice(0, 6)
    }
    // comprehensive includes all sections

    // Format sections
    sectionsToInclude.forEach(section => {
      formatted += `## ${section.title}\n\n${section.content}\n\n`
    })

    // Add follow-up questions for detailed and comprehensive styles
    if (style !== 'concise' && response.followUpQuestions.length > 0) {
      formatted += `## 🤔 Perguntas para Aprofundamento\n\n`
      response.followUpQuestions.forEach((question, index) => {
        formatted += `${index + 1}. ${question}\n`
      })
      formatted += '\n'
    }

    // Add action items
    if (response.actionItems.length > 0) {
      formatted += `## ✅ Itens de Ação\n\n`
      response.actionItems.forEach((item, index) => {
        formatted += `- [ ] ${item}\n`
      })
      formatted += '\n'
    }

    // Add metadata footer for comprehensive style
    if (style === 'comprehensive') {
      formatted += `---\n\n`
      formatted += `**Tempo de leitura estimado:** ${response.estimatedReadTime} min | `
      formatted += `**Confiança:** ${Math.round(response.confidence * 100)}% | `
      formatted += `**Completude:** ${Math.round(response.completeness * 100)}%\n`
    }

    return formatted
  }

  private calculateMetadata(
    analysis: AdvancedAnalysis,
    response: EnhancedResponse,
    processingTime: number,
    config: AgentConfig
  ): ResponseMetadata {
    // Calculate quality score based on multiple factors
    let qualityScore = 0

    // Content quality (40%)
    qualityScore += response.completeness * 0.4

    // Analysis depth (30%)
    const analysisDepth = (analysis.namedEntities.length > 0 ? 0.1 : 0) +
      (analysis.topicClusters.length > 0 ? 0.1 : 0) +
      (analysis.riskFactors.length > 0 ? 0.05 : 0) +
      (analysis.opportunityIndicators.length > 0 ? 0.05 : 0)
    qualityScore += Math.min(analysisDepth, 0.3)

    // Response structure (20%)
    const structureScore = Math.min(response.sections.length * 0.03, 0.2)
    qualityScore += structureScore

    // Actionability (10%)
    const actionabilityScore = Math.min(response.actionItems.length * 0.02, 0.1)
    qualityScore += actionabilityScore

    return {
      processingTime,
      confidence: response.confidence,
      completeness: response.completeness,
      qualityScore: Math.min(1.0, qualityScore),
      templateUsed: 'dynamic', // Could be more specific
      riskFactors: analysis.riskFactors.length,
      opportunities: analysis.opportunityIndicators.length,
      followUpRecommended: response.followUpQuestions.length > 0 || analysis.completeness < 0.7
    }
  }

  private generateResponseId(): string {
    return `resp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// Export singleton instance
export const enhancedAgentSystem = EnhancedAgentSystem.getInstance()

// Utility function for easy access
export async function generateAgentResponse(
  agentId: string,
  input: string,
  userContext?: Record<string, any>
): Promise<AgentResponse> {
  return enhancedAgentSystem.generateResponse(agentId, input, userContext)
}

// Utility function for input analysis
export function analyzeUserInput(input: string): AdvancedAnalysis {
  return enhancedAgentSystem.analyzeInput(input)
}
