/**
 * Context-Aware Response Templates System
 *
 * This module provides dynamic response templates that adapt based on input analysis,
 * user context, and agent specialization to generate comprehensive, structured responses.
 */

import { InputAnalysis, ResponseSection } from './responseEngine'
import { AgentKnowledgeBase } from './knowledgeBase'

export interface TemplateContext {
  agentId: string
  analysis: InputAnalysis
  input: string
  userContext?: Record<string, any>
}

export interface DynamicTemplate {
  id: string
  name: string
  description: string
  applicableWhen: (context: TemplateContext) => boolean
  priority: number
  sections: TemplateSectionGenerator[]
}

export interface TemplateSectionGenerator {
  id: string
  title: string
  type: 'analysis' | 'recommendation' | 'example' | 'warning' | 'next-steps' | 'best-practices' | 'detailed-guide'
  priority: number
  required: boolean
  condition?: (context: TemplateContext) => boolean
  generator: (context: TemplateContext) => ResponseSection
}

export class ResponseTemplateEngine {
  private static instance: ResponseTemplateEngine
  private templates: Map<string, DynamicTemplate[]> = new Map()
  private knowledgeBase: AgentKnowledgeBase

  private constructor() {
    this.knowledgeBase = AgentKnowledgeBase.getInstance()
    this.initializeTemplates()
  }

  public static getInstance(): ResponseTemplateEngine {
    if (!ResponseTemplateEngine.instance) {
      ResponseTemplateEngine.instance = new ResponseTemplateEngine()
    }
    return ResponseTemplateEngine.instance
  }

  public generateResponse(context: TemplateContext): ResponseSection[] {
    console.log(`Generating response for agent: ${context.agentId}`)
    const agentTemplates = this.templates.get(context.agentId) || []
    console.log(`Found ${agentTemplates.length} templates for agent`)

    // Find applicable templates
    const applicableTemplates = agentTemplates
      .filter(template => {
        const applicable = template.applicableWhen(context)
        console.log(`Template ${template.name} applicable: ${applicable}`)
        return applicable
      })
      .sort((a, b) => b.priority - a.priority)

    console.log(`${applicableTemplates.length} applicable templates found`)

    // Use the highest priority applicable template
    const selectedTemplate = applicableTemplates[0]
    if (!selectedTemplate) {
      console.log('No applicable template found, using fallback')
      return this.generateFallbackResponse(context)
    }

    console.log(`Using template: ${selectedTemplate.name}`)

    // Generate sections from template
    const sections: ResponseSection[] = []

    for (const sectionGen of selectedTemplate.sections) {
      // Check if section condition is met (if any)
      if (sectionGen.condition && !sectionGen.condition(context)) {
        console.log(`Skipping section ${sectionGen.id} - condition not met`)
        continue
      }

      try {
        console.log(`Generating section: ${sectionGen.id}`)
        const section = sectionGen.generator(context)
        sections.push(section)
        console.log(`Section ${sectionGen.id} generated successfully`)
      } catch (error) {
        console.error(`Error generating section ${sectionGen.id}:`, error)
        // Continue with other sections
      }
    }

    console.log(`Generated ${sections.length} sections total`)
    // Sort sections by priority
    return sections.sort((a, b) => b.priority - a.priority)
  }

  private initializeTemplates(): void {
    // Atendimento Agent Templates
    this.templates.set('atendimento', [
      {
        id: 'comprehensive_briefing',
        name: 'Briefing Abrangente',
        description: 'Template completo para análise e estruturação de briefings',
        applicableWhen: (context) => {
          // Always apply for atendimento agent - this is the primary template
          console.log('Checking briefing template applicability:', {
            intent: context.analysis.intent,
            keywords: context.analysis.keywords,
            hasKeywords: context.analysis.keywords.length > 0
          })
          return true // Always applicable for atendimento
        },
        priority: 10,
        sections: [
          {
            id: 'executive_summary',
            title: '📋 Resumo Executivo',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generateExecutiveSummary(context)
          },
          {
            id: 'detailed_analysis',
            title: '🔍 Análise Detalhada do Briefing',
            type: 'analysis',
            priority: 9,
            required: true,
            generator: (context) => this.generateDetailedAnalysis(context)
          },
          {
            id: 'missing_information',
            title: '❓ Informações Necessárias',
            type: 'warning',
            priority: 8,
            required: false,
            condition: (context) => context.analysis.completeness < 0.7,
            generator: (context) => this.generateMissingInformation(context)
          },
          {
            id: 'strategic_recommendations',
            title: '💡 Recomendações Estratégicas',
            type: 'recommendation',
            priority: 8,
            required: true,
            generator: (context) => this.generateStrategicRecommendations(context)
          },
          {
            id: 'timeline_breakdown',
            title: '📅 Cronograma Detalhado',
            type: 'detailed-guide',
            priority: 7,
            required: true,
            generator: (context) => this.generateTimelineBreakdown(context)
          },
          {
            id: 'approval_process',
            title: '✅ Processo de Aprovação',
            type: 'detailed-guide',
            priority: 6,
            required: true,
            generator: (context) => this.generateApprovalProcess(context)
          },
          {
            id: 'risk_assessment',
            title: '⚠️ Análise de Riscos',
            type: 'warning',
            priority: 7,
            required: false,
            condition: (context) => context.analysis.urgency === 'high' || context.analysis.complexity === 'advanced',
            generator: (context) => this.generateRiskAssessment(context)
          },
          {
            id: 'best_practices',
            title: '🎯 Melhores Práticas',
            type: 'best-practices',
            priority: 5,
            required: false,
            generator: (context) => this.generateBestPractices(context)
          },
          {
            id: 'next_steps',
            title: '🚀 Próximos Passos',
            type: 'next-steps',
            priority: 9,
            required: true,
            generator: (context) => this.generateNextSteps(context)
          }
        ]
      },
      {
        id: 'client_communication',
        name: 'Comunicação com Cliente',
        description: 'Template para situações de comunicação e alinhamento com cliente',
        applicableWhen: (context) =>
          context.analysis.keywords.some(k => ['cliente', 'comunicação', 'alinhamento', 'reunião'].includes(k)),
        priority: 8,
        sections: [
          {
            id: 'communication_analysis',
            title: '📞 Análise da Situação',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generateCommunicationAnalysis(context)
          },
          {
            id: 'communication_strategy',
            title: '💬 Estratégia de Comunicação',
            type: 'recommendation',
            priority: 9,
            required: true,
            generator: (context) => this.generateCommunicationStrategy(context)
          },
          {
            id: 'meeting_agenda',
            title: '📋 Agenda de Reunião Sugerida',
            type: 'detailed-guide',
            priority: 7,
            required: false,
            condition: (context) => context.analysis.keywords.includes('reunião'),
            generator: (context) => this.generateMeetingAgenda(context)
          }
        ]
      }
    ])

    // Planejamento Agent Templates
    this.templates.set('planejamento', [
      {
        id: 'strategic_planning',
        name: 'Planejamento Estratégico',
        description: 'Template completo para planejamento de campanhas',
        applicableWhen: (context) =>
          context.analysis.intent.includes('plan') ||
          context.analysis.keywords.includes('planejamento') ||
          context.analysis.keywords.includes('estratégia'),
        priority: 10,
        sections: [
          {
            id: 'strategic_overview',
            title: '🎯 Visão Estratégica',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generateStrategicOverview(context)
          },
          {
            id: 'market_analysis',
            title: '📊 Análise de Mercado',
            type: 'analysis',
            priority: 9,
            required: true,
            generator: (context) => this.generateMarketAnalysis(context)
          },
          {
            id: 'campaign_phases',
            title: '📅 Fases da Campanha',
            type: 'detailed-guide',
            priority: 8,
            required: true,
            generator: (context) => this.generateCampaignPhases(context)
          },
          {
            id: 'budget_allocation',
            title: '💰 Distribuição de Budget',
            type: 'detailed-guide',
            priority: 8,
            required: true,
            generator: (context) => this.generateBudgetAllocation(context)
          },
          {
            id: 'kpi_framework',
            title: '📈 Framework de KPIs',
            type: 'detailed-guide',
            priority: 7,
            required: true,
            generator: (context) => this.generateKPIFramework(context)
          }
        ]
      }
    ])

    // Mídia Agent Templates
    this.templates.set('midia', [
      {
        id: 'performance_analysis',
        name: 'Análise de Performance',
        description: 'Template para análise detalhada de performance de campanhas',
        applicableWhen: (context) =>
          context.analysis.intent.includes('analyze') ||
          context.analysis.keywords.some(k => ['performance', 'resultado', 'análise', 'métricas'].includes(k)),
        priority: 10,
        sections: [
          {
            id: 'performance_overview',
            title: '📊 Visão Geral da Performance',
            type: 'analysis',
            priority: 10,
            required: true,
            generator: (context) => this.generatePerformanceOverview(context)
          },
          {
            id: 'detailed_metrics',
            title: '📈 Métricas Detalhadas',
            type: 'analysis',
            priority: 9,
            required: true,
            generator: (context) => this.generateDetailedMetrics(context)
          },
          {
            id: 'optimization_opportunities',
            title: '🎯 Oportunidades de Otimização',
            type: 'recommendation',
            priority: 8,
            required: true,
            generator: (context) => this.generateOptimizationOpportunities(context)
          },
          {
            id: 'competitive_insights',
            title: '🏆 Insights Competitivos',
            type: 'analysis',
            priority: 7,
            required: false,
            generator: (context) => this.generateCompetitiveInsights(context)
          }
        ]
      }
    ])
  }

  // Section generators implementation
  private generateExecutiveSummary(context: TemplateContext): ResponseSection {
    console.log('Generating executive summary...')
    const { analysis, input } = context

    console.log('Analysis data:', {
      completeness: analysis.completeness,
      complexity: analysis.complexity,
      urgency: analysis.urgency,
      intent: analysis.intent
    })

    let content = `**Status:** Briefing analisado com ${Math.round(analysis.completeness * 100)}% de completude\n`
    content += `**Complexidade:** ${this.translateComplexity(analysis.complexity)}\n`
    content += `**Urgência:** ${this.translateUrgency(analysis.urgency)}\n`
    content += `**Intenção Principal:** ${this.translateIntent(analysis.intent)}\n\n`

    if (analysis.context.budget) {
      content += `**Budget Identificado:** R$ ${analysis.context.budget}\n`
    }
    if (analysis.context.timeline) {
      content += `**Prazo:** ${analysis.context.timeline}\n`
    }
    if (analysis.context.channels && analysis.context.channels.length > 0) {
      content += `**Canais Mencionados:** ${analysis.context.channels.join(', ')}\n`
    }

    return {
      title: '📋 Resumo Executivo',
      content,
      type: 'analysis',
      priority: 10,
      metadata: { confidence: analysis.completeness }
    }
  }

  private generateDetailedAnalysis(context: TemplateContext): ResponseSection {
    const { analysis, input } = context

    let content = '### Elementos Identificados\n\n'

    if (analysis.keywords.length > 0) {
      content += `**Palavras-chave principais:** ${analysis.keywords.slice(0, 5).join(', ')}\n\n`
    }

    if (analysis.entities.length > 0) {
      content += `**Entidades detectadas:** ${analysis.entities.join(', ')}\n\n`
    }

    content += '### Contexto do Projeto\n\n'

    if (Object.keys(analysis.context).length > 0) {
      for (const [key, value] of Object.entries(analysis.context)) {
        content += `• **${this.formatContextKey(key)}:** ${value}\n`
      }
    } else {
      content += '• Contexto adicional será coletado durante o processo de briefing\n'
    }

    content += '\n### Análise de Sentimento\n\n'
    content += `O tom geral da solicitação é **${this.translateSentiment(analysis.sentiment)}**, `

    switch (analysis.sentiment) {
      case 'positive':
        content += 'indicando confiança e expectativas positivas para o projeto.'
        break
      case 'negative':
        content += 'sugerindo possíveis preocupações ou urgência que devem ser endereçadas.'
        break
      default:
        content += 'mantendo um tom profissional e objetivo.'
    }

    return {
      title: '🔍 Análise Detalhada do Briefing',
      content,
      type: 'analysis',
      priority: 9
    }
  }

  private generateMissingInformation(context: TemplateContext): ResponseSection {
    const { analysis } = context
    const missing: string[] = []

    if (!analysis.context.budget) missing.push('Orçamento disponível')
    if (!analysis.context.timeline) missing.push('Cronograma desejado')
    if (!analysis.context.targetAge) missing.push('Público-alvo detalhado')
    if (!analysis.context.channels) missing.push('Canais preferenciais')

    let content = '### Informações Essenciais Faltantes\n\n'

    if (missing.length > 0) {
      missing.forEach(item => {
        content += `• ${item}\n`
      })

      content += '\n### Impacto da Falta de Informações\n\n'
      content += '• **Planejamento:** Pode resultar em cronograma impreciso\n'
      content += '• **Orçamento:** Dificulta estimativa de custos e ROI\n'
      content += '• **Estratégia:** Limita precisão das recomendações\n'
      content += '• **Execução:** Pode causar retrabalho e atrasos\n'

      content += '\n### Recomendação\n\n'
      content += 'Agendar reunião de alinhamento para coletar informações faltantes antes de prosseguir com o planejamento detalhado.'
    } else {
      content += 'Todas as informações essenciais foram fornecidas. Excelente briefing!'
    }

    return {
      title: '❓ Informações Necessárias',
      content,
      type: 'warning',
      priority: 8
    }
  }

  private generateStrategicRecommendations(context: TemplateContext): ResponseSection {
    const { analysis, input } = context
    const knowledge = this.knowledgeBase.getAgentKnowledge(context.agentId)
    const insights = this.knowledgeBase.getInsights(context.agentId, analysis)

    let content = '### Recomendações Baseadas na Análise\n\n'

    // Add insights from knowledge base
    insights.forEach((insight, index) => {
      content += `${index + 1}. **${insight.priority.toUpperCase()}:** ${insight.insight}\n\n`
    })

    // Add specific recommendations based on analysis
    if (analysis.urgency === 'high') {
      content += '• **Recursos Adicionais:** Considere alocar recursos extras para garantir qualidade\n'
      content += '• **Comunicação Intensiva:** Estabeleça check-ins diários com o cliente\n'
      content += '• **Cronograma Acelerado:** Defina marcos intermediários para controle\n\n'
    }

    if (analysis.complexity === 'advanced') {
      content += '• **Especialistas:** Envolver especialistas desde o início do projeto\n'
      content += '• **Faseamento:** Dividir projeto em fases menores para melhor controle\n'
      content += '• **Documentação:** Manter registro detalhado de todas as decisões\n\n'
    }

    // Budget-based recommendations
    if (analysis.context.budget) {
      const budgetValue = parseFloat(analysis.context.budget.replace(/[^\d]/g, ''))
      if (budgetValue < 10000) {
        content += '• **Foco Estratégico:** Com orçamento limitado, concentre em 1-2 canais principais\n'
        content += '• **ROI Máximo:** Priorize ações com maior potencial de retorno\n\n'
      } else if (budgetValue > 100000) {
        content += '• **Diversificação:** Budget permite estratégia multi-canal robusta\n'
        content += '• **Testes A/B:** Invista em testes para otimização contínua\n\n'
      }
    }

    // Channel-specific recommendations
    if (analysis.context.channels) {
      content += '• **Integração de Canais:** Garanta mensagem consistente entre ' + analysis.context.channels.join(', ') + '\n'
      content += '• **Attribution:** Configure tracking para medir performance de cada canal\n\n'
    }

    // Default recommendations if no specific insights
    if (insights.length === 0 && analysis.urgency === 'low' && analysis.complexity === 'basic') {
      content += '• **Planejamento Estruturado:** Defina objetivos claros e mensuráveis\n'
      content += '• **Cronograma Realista:** Estabeleça prazos factíveis para cada etapa\n'
      content += '• **Comunicação Regular:** Mantenha cliente informado sobre progresso\n'
      content += '• **Métricas de Sucesso:** Defina KPIs específicos para acompanhamento\n'
    }

    return {
      title: '💡 Recomendações Estratégicas',
      content,
      type: 'recommendation',
      priority: 8
    }
  }

  private generateNextSteps(context: TemplateContext): ResponseSection {
    const { analysis, input, agentId } = context

    let content = '### Ações Imediatas (24-48h)\n\n'

    // Agent-specific immediate actions
    if (agentId === 'atendimento') {
      content += '1. ✅ Validar briefing com cliente\n'
      if (analysis.completeness < 0.7) {
        content += '2. 📋 Solicitar informações faltantes (ver seção de informações necessárias)\n'
      } else {
        content += '2. 📋 Confirmar detalhes do briefing\n'
      }
      content += '3. 👥 Definir equipe do projeto e responsabilidades\n'
      content += '4. 📅 Estabelecer cronograma de aprovações\n'
    } else if (agentId === 'planejamento') {
      content += '1. 📊 Analisar dados históricos e benchmarks\n'
      content += '2. 🎯 Definir personas detalhadas\n'
      content += '3. 📈 Estabelecer KPIs e métricas de sucesso\n'
      content += '4. 💰 Validar distribuição de budget\n'
    } else if (agentId === 'midia') {
      content += '1. 📊 Configurar tracking e analytics\n'
      content += '2. 🎯 Definir segmentações iniciais\n'
      content += '3. 💡 Criar primeiros criativos para teste\n'
      content += '4. 📈 Estabelecer baseline de performance\n'
    }

    if (analysis.urgency === 'high') {
      content += '5. 🚨 Ativar protocolo de urgência (recursos extras, comunicação diária)\n'
    }

    content += '\n### Próxima Semana\n\n'

    // Week 1 actions based on agent and context
    if (analysis.context.budget) {
      const budgetValue = parseFloat(analysis.context.budget.replace(/[^\d]/g, ''))
      if (budgetValue > 50000) {
        content += '• Desenvolver estratégia multi-canal detalhada\n'
        content += '• Planejar testes A/B para otimização\n'
      } else {
        content += '• Focar estratégia em canais de maior ROI\n'
        content += '• Otimizar budget para máximo impacto\n'
      }
    } else {
      content += '• Desenvolver estratégia baseada em objetivos\n'
    }

    content += '• Criar cronograma executivo detalhado\n'
    content += '• Definir marcos de aprovação e entrega\n'

    if (agentId === 'atendimento') {
      content += '• Preparar primeira apresentação para cliente\n'
      content += '• Estabelecer canais de comunicação\n'
    } else if (agentId === 'planejamento') {
      content += '• Finalizar estratégia e táticas\n'
      content += '• Preparar briefings para equipe criativa\n'
    } else if (agentId === 'midia') {
      content += '• Configurar campanhas iniciais\n'
      content += '• Preparar dashboards de monitoramento\n'
    }

    content += '\n### Próximas 2-4 Semanas\n\n'
    content += '• Implementar estratégia aprovada\n'
    content += '• Monitorar performance inicial\n'
    content += '• Ajustar táticas baseado em dados\n'
    content += '• Preparar relatórios de progresso\n'

    content += '\n### Acompanhamento\n\n'
    if (analysis.urgency === 'high') {
      content += '• **Check-ins:** Diários até estabilização\n'
      content += '• **Relatórios:** Status reports a cada 3 dias\n'
    } else {
      content += '• **Check-ins:** Semanais com cliente\n'
      content += '• **Relatórios:** Status reports quinzenais\n'
    }
    content += '• **Revisões:** Marcos de aprovação pré-definidos\n'
    content += '• **Otimização:** Ajustes baseados em performance\n'

    return {
      title: '🚀 Próximos Passos',
      content,
      type: 'next-steps',
      priority: 9
    }
  }

  private generateTimelineBreakdown(context: TemplateContext): ResponseSection {
    const { analysis, agentId } = context

    let content = '### Cronograma Sugerido\n\n'

    // Determine timeline based on context
    let totalWeeks = 4 // default
    if (analysis.context.timeline) {
      const timelineText = analysis.context.timeline.toLowerCase()
      if (timelineText.includes('dia')) {
        const days = parseInt(timelineText.match(/\d+/)?.[0] || '30')
        totalWeeks = Math.ceil(days / 7)
      } else if (timelineText.includes('semana')) {
        totalWeeks = parseInt(timelineText.match(/\d+/)?.[0] || '4')
      } else if (timelineText.includes('mes')) {
        const months = parseInt(timelineText.match(/\d+/)?.[0] || '1')
        totalWeeks = months * 4
      }
    }

    if (analysis.urgency === 'high') {
      totalWeeks = Math.max(2, Math.ceil(totalWeeks * 0.7)) // Reduce by 30% for urgent projects
    }

    // Generate phase-based timeline
    if (totalWeeks <= 2) {
      content += '**⚡ Cronograma Acelerado (2 semanas)**\n\n'
      content += '**Semana 1:**\n'
      content += '• Dias 1-2: Briefing e planejamento\n'
      content += '• Dias 3-5: Desenvolvimento e produção\n'
      content += '• Dias 6-7: Revisões e ajustes\n\n'
      content += '**Semana 2:**\n'
      content += '• Dias 8-10: Implementação\n'
      content += '• Dias 11-12: Testes e otimização\n'
      content += '• Dias 13-14: Lançamento e monitoramento\n'
    } else if (totalWeeks <= 4) {
      content += '**📅 Cronograma Padrão (4 semanas)**\n\n'
      content += '**Semana 1 - Planejamento:**\n'
      content += '• Validação de briefing e objetivos\n'
      content += '• Pesquisa de mercado e concorrência\n'
      content += '• Definição de estratégia e táticas\n'
      content += '• Aprovação de conceitos iniciais\n\n'
      content += '**Semana 2 - Desenvolvimento:**\n'
      content += '• Criação de materiais e conteúdo\n'
      content += '• Desenvolvimento de criativos\n'
      content += '• Setup de campanhas e tracking\n'
      content += '• Testes internos e ajustes\n\n'
      content += '**Semana 3 - Implementação:**\n'
      content += '• Lançamento das campanhas\n'
      content += '• Monitoramento inicial\n'
      content += '• Primeiros ajustes baseados em dados\n'
      content += '• Relatório de performance inicial\n\n'
      content += '**Semana 4 - Otimização:**\n'
      content += '• Análise de resultados\n'
      content += '• Otimizações baseadas em performance\n'
      content += '• Relatório final e recomendações\n'
      content += '• Planejamento de continuidade\n'
    } else {
      content += `**📈 Cronograma Estendido (${totalWeeks} semanas)**\n\n`
      const phaseDuration = Math.ceil(totalWeeks / 4)
      content += `**Fase 1 - Planejamento (${phaseDuration} semanas):**\n`
      content += '• Pesquisa aprofundada e análise de mercado\n'
      content += '• Desenvolvimento de estratégia detalhada\n'
      content += '• Criação de personas e jornada do cliente\n'
      content += '• Aprovação de conceitos e direcionamento\n\n'
      content += `**Fase 2 - Desenvolvimento (${phaseDuration} semanas):**\n`
      content += '• Produção de materiais e conteúdo\n'
      content += '• Desenvolvimento de criativos variados\n'
      content += '• Setup completo de campanhas\n'
      content += '• Testes A/B e validações\n\n'
      content += `**Fase 3 - Lançamento (${phaseDuration} semanas):**\n`
      content += '• Lançamento faseado das campanhas\n'
      content += '• Monitoramento intensivo\n'
      content += '• Otimizações contínuas\n'
      content += '• Expansão baseada em resultados\n\n'
      content += `**Fase 4 - Consolidação (${totalWeeks - (phaseDuration * 3)} semanas):**\n`
      content += '• Análise completa de resultados\n'
      content += '• Documentação de aprendizados\n'
      content += '• Recomendações para continuidade\n'
      content += '• Planejamento de próximas fases\n'
    }

    content += '\n### Marcos Críticos\n\n'
    content += '🎯 **Aprovação de Estratégia:** Fim da primeira fase\n'
    content += '🎨 **Aprovação de Criativos:** Meio do desenvolvimento\n'
    content += '🚀 **Go-Live:** Início da implementação\n'
    content += '📊 **Primeira Análise:** 1 semana após lançamento\n'

    return {
      title: '📅 Cronograma Detalhado',
      content,
      type: 'detailed-guide',
      priority: 7
    }
  }

  private generateApprovalProcess(context: TemplateContext): ResponseSection {
    const { analysis, agentId } = context

    let content = '### Fluxo de Aprovações\n\n'

    content += '**1. Aprovação de Estratégia**\n'
    content += '• Apresentação de briefing estruturado\n'
    content += '• Validação de objetivos e KPIs\n'
    content += '• Aprovação de cronograma e budget\n'
    content += '• **Prazo:** 2-3 dias úteis\n\n'

    content += '**2. Aprovação de Conceitos**\n'
    content += '• Apresentação de direcionamento criativo\n'
    content += '• Validação de tom de voz e messaging\n'
    content += '• Aprovação de abordagem estratégica\n'
    content += '• **Prazo:** 3-5 dias úteis\n\n'

    content += '**3. Aprovação de Materiais**\n'
    content += '• Revisão de peças criativas\n'
    content += '• Validação de textos e copy\n'
    content += '• Aprovação final para produção\n'
    content += '• **Prazo:** 2-3 dias úteis\n\n'

    if (analysis.urgency === 'high') {
      content += '### ⚡ Processo Acelerado\n\n'
      content += '• **Aprovações Simultâneas:** Conceito + materiais juntos\n'
      content += '• **Prazo Reduzido:** 24-48h por aprovação\n'
      content += '• **Comunicação Direta:** Calls ao invés de e-mails\n'
      content += '• **Aprovação Condicional:** Implementar com ajustes menores\n\n'
    }

    content += '### Responsabilidades\n\n'
    content += '**Cliente:**\n'
    content += '• Feedback em até 48h (ou 24h se urgente)\n'
    content += '• Consolidação de comentários internos\n'
    content += '• Decisões finais dentro do prazo\n\n'

    content += '**Agência:**\n'
    content += '• Apresentações claras e objetivas\n'
    content += '• Justificativas estratégicas\n'
    content += '• Implementação de ajustes solicitados\n'
    content += '• Comunicação proativa sobre prazos\n'

    return {
      title: '✅ Processo de Aprovação',
      content,
      type: 'detailed-guide',
      priority: 6
    }
  }

  private generateRiskAssessment(context: TemplateContext): ResponseSection {
    const { analysis } = context

    let content = '### Riscos Identificados\n\n'

    // Add risks from analysis
    if (analysis.riskFactors && analysis.riskFactors.length > 0) {
      analysis.riskFactors.forEach((risk, index) => {
        const severityEmoji = {
          'low': '🟡',
          'medium': '🟠',
          'high': '🔴',
          'critical': '🚨'
        }[risk.severity] || '⚠️'

        content += `**${severityEmoji} ${risk.type.toUpperCase()} - ${risk.severity.toUpperCase()}**\n`
        content += `${risk.description}\n`
        content += `**Probabilidade:** ${Math.round(risk.probability * 100)}% | **Impacto:** ${Math.round(risk.impact * 100)}%\n`
        content += '**Mitigação:**\n'
        risk.mitigation.forEach(action => {
          content += `• ${action}\n`
        })
        content += '\n'
      })
    } else {
      // Generate common risks based on analysis
      if (analysis.urgency === 'high') {
        content += '**🔴 CRONOGRAMA - ALTO**\n'
        content += 'Prazo apertado pode comprometer qualidade ou gerar estresse na equipe\n'
        content += '**Mitigação:**\n'
        content += '• Alocar recursos adicionais\n'
        content += '• Simplificar escopo se necessário\n'
        content += '• Comunicação intensiva com cliente\n\n'
      }

      if (analysis.completeness < 0.6) {
        content += '**🟠 BRIEFING - MÉDIO**\n'
        content += 'Informações incompletas podem gerar retrabalho e atrasos\n'
        content += '**Mitigação:**\n'
        content += '• Reunião de alinhamento urgente\n'
        content += '• Checklist de informações obrigatórias\n'
        content += '• Validação constante com cliente\n\n'
      }

      if (analysis.complexity === 'advanced') {
        content += '**🟠 COMPLEXIDADE - MÉDIO**\n'
        content += 'Projeto complexo requer expertise específica e coordenação cuidadosa\n'
        content += '**Mitigação:**\n'
        content += '• Envolver especialistas desde o início\n'
        content += '• Dividir em fases menores\n'
        content += '• Documentação detalhada de processos\n\n'
      }
    }

    content += '### Monitoramento de Riscos\n\n'
    content += '• **Revisões Semanais:** Avaliação de status dos riscos\n'
    content += '• **Alertas Antecipados:** Comunicação imediata se risco se materializar\n'
    content += '• **Planos de Contingência:** Ações alternativas preparadas\n'
    content += '• **Escalação:** Processo claro para decisões críticas\n'

    return {
      title: '⚠️ Análise de Riscos',
      content,
      type: 'warning',
      priority: 7
    }
  }

  private generateBestPractices(context: TemplateContext): ResponseSection {
    const { analysis, agentId } = context
    const knowledge = this.knowledgeBase.getAgentKnowledge(agentId)

    let content = '### Práticas Recomendadas\n\n'

    // Add best practices from knowledge base
    if (knowledge && knowledge.bestPractices.length > 0) {
      const relevantPractices = knowledge.bestPractices.slice(0, 2) // Top 2 most relevant

      relevantPractices.forEach(practice => {
        content += `**${practice.title}**\n`
        content += `${practice.description}\n\n`
        content += '**Implementação:**\n'
        practice.implementation.forEach(step => {
          content += `• ${step}\n`
        })
        content += '\n**Benefícios:**\n'
        practice.benefits.forEach(benefit => {
          content += `• ${benefit}\n`
        })
        content += '\n**Evitar:**\n'
        practice.commonMistakes.forEach(mistake => {
          content += `• ${mistake}\n`
        })
        content += '\n'
      })
    }

    // Add general best practices based on context
    if (analysis.context.budget) {
      content += '**Gestão de Budget**\n'
      content += '• Monitore gastos semanalmente\n'
      content += '• Reserve 10-15% para otimizações\n'
      content += '• Documente todas as decisões de investimento\n'
      content += '• Meça ROI de cada canal separadamente\n\n'
    }

    if (analysis.context.channels && analysis.context.channels.length > 1) {
      content += '**Integração Multi-canal**\n'
      content += '• Mantenha mensagem consistente entre canais\n'
      content += '• Configure attribution modeling adequado\n'
      content += '• Teste criativos específicos para cada canal\n'
      content += '• Monitore performance comparativa\n\n'
    }

    content += '**Comunicação com Cliente**\n'
    content += '• Updates regulares mesmo sem novidades\n'
    content += '• Dados sempre acompanhados de insights\n'
    content += '• Transparência total sobre desafios\n'
    content += '• Recomendações proativas de melhorias\n'

    return {
      title: '🎯 Melhores Práticas',
      content,
      type: 'best-practices',
      priority: 5
    }
  }

  private generateCommunicationAnalysis(context: TemplateContext): ResponseSection {
    return { title: '📞 Análise da Situação', content: 'Análise da comunicação...', type: 'analysis', priority: 10 }
  }

  private generateCommunicationStrategy(context: TemplateContext): ResponseSection {
    return { title: '💬 Estratégia de Comunicação', content: 'Estratégia de comunicação...', type: 'recommendation', priority: 9 }
  }

  private generateMeetingAgenda(context: TemplateContext): ResponseSection {
    return { title: '📋 Agenda de Reunião Sugerida', content: 'Agenda de reunião...', type: 'detailed-guide', priority: 7 }
  }

  private generateStrategicOverview(context: TemplateContext): ResponseSection {
    return { title: '🎯 Visão Estratégica', content: 'Visão estratégica...', type: 'analysis', priority: 10 }
  }

  private generateMarketAnalysis(context: TemplateContext): ResponseSection {
    return { title: '📊 Análise de Mercado', content: 'Análise de mercado...', type: 'analysis', priority: 9 }
  }

  private generateCampaignPhases(context: TemplateContext): ResponseSection {
    return { title: '📅 Fases da Campanha', content: 'Fases da campanha...', type: 'detailed-guide', priority: 8 }
  }

  private generateBudgetAllocation(context: TemplateContext): ResponseSection {
    return { title: '💰 Distribuição de Budget', content: 'Distribuição de budget...', type: 'detailed-guide', priority: 8 }
  }

  private generateKPIFramework(context: TemplateContext): ResponseSection {
    return { title: '📈 Framework de KPIs', content: 'Framework de KPIs...', type: 'detailed-guide', priority: 7 }
  }

  private generatePerformanceOverview(context: TemplateContext): ResponseSection {
    return { title: '📊 Visão Geral da Performance', content: 'Visão geral da performance...', type: 'analysis', priority: 10 }
  }

  private generateDetailedMetrics(context: TemplateContext): ResponseSection {
    return { title: '📈 Métricas Detalhadas', content: 'Métricas detalhadas...', type: 'analysis', priority: 9 }
  }

  private generateOptimizationOpportunities(context: TemplateContext): ResponseSection {
    return { title: '🎯 Oportunidades de Otimização', content: 'Oportunidades de otimização...', type: 'recommendation', priority: 8 }
  }

  private generateCompetitiveInsights(context: TemplateContext): ResponseSection {
    return { title: '🏆 Insights Competitivos', content: 'Insights competitivos...', type: 'analysis', priority: 7 }
  }

  private generateFallbackResponse(context: TemplateContext): ResponseSection[] {
    console.log('Generating fallback response for agent:', context.agentId)

    // Generate basic sections using the existing generators
    const sections: ResponseSection[] = []

    try {
      sections.push(this.generateExecutiveSummary(context))
    } catch (error) {
      console.error('Error generating executive summary in fallback:', error)
    }

    try {
      sections.push(this.generateStrategicRecommendations(context))
    } catch (error) {
      console.error('Error generating recommendations in fallback:', error)
    }

    try {
      sections.push(this.generateNextSteps(context))
    } catch (error) {
      console.error('Error generating next steps in fallback:', error)
    }

    // If all generators failed, return a basic response
    if (sections.length === 0) {
      sections.push({
        title: '📋 Análise Básica',
        content: `Análise do input recebido para o agente ${context.agentId}.\n\nInput analisado: "${context.input}"\n\nRecomendamos fornecer mais detalhes para uma análise mais completa.`,
        type: 'analysis',
        priority: 5
      })
    }

    return sections
  }

  // Utility methods
  private translateComplexity(complexity: string): string {
    const translations = {
      'basic': 'Básica',
      'intermediate': 'Intermediária',
      'advanced': 'Avançada'
    }
    return translations[complexity as keyof typeof translations] || complexity
  }

  private translateUrgency(urgency: string): string {
    const translations = {
      'low': 'Baixa',
      'medium': 'Média',
      'high': 'Alta'
    }
    return translations[urgency as keyof typeof translations] || urgency
  }

  private translateIntent(intent: string): string {
    const translations = {
      'create_briefing': 'Criação de Briefing',
      'analyze_performance': 'Análise de Performance',
      'plan_campaign': 'Planejamento de Campanha',
      'optimize': 'Otimização',
      'report': 'Relatório',
      'troubleshoot': 'Resolução de Problemas',
      'general_inquiry': 'Consulta Geral'
    }
    return translations[intent as keyof typeof translations] || intent
  }

  private translateSentiment(sentiment: string): string {
    const translations = {
      'positive': 'positivo',
      'neutral': 'neutro',
      'negative': 'negativo'
    }
    return translations[sentiment as keyof typeof translations] || sentiment
  }

  private formatContextKey(key: string): string {
    const translations = {
      'budget': 'Orçamento',
      'timeline': 'Cronograma',
      'targetAge': 'Faixa Etária',
      'channels': 'Canais'
    }
    return translations[key as keyof typeof translations] || key
  }
}
