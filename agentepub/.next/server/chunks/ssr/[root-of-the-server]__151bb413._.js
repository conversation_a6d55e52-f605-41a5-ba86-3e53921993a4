module.exports = [
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/components/auth/ProtectedRoute.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "LoadingSpinner",
    ()=>LoadingSpinner,
    "default",
    ()=>ProtectedRoute,
    "useAuthGuard",
    ()=>useAuthGuard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function ProtectedRoute({ children, redirectTo = '/' }) {
    const { user, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [forceShowContent, setForceShowContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!loading && !user) {
            console.log('No user found, redirecting to:', redirectTo);
            router.push(redirectTo);
        }
    }, [
        user,
        loading,
        router,
        redirectTo
    ]);
    // Safety timeout to prevent infinite loading
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const timeout = setTimeout(()=>{
            if (loading) {
                console.warn('ProtectedRoute loading timeout - forcing content display');
                setForceShowContent(true);
            }
        }, 8000) // 8 seconds timeout
        ;
        return ()=>clearTimeout(timeout);
    }, [
        loading
    ]);
    if (loading && !forceShowContent) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center bg-gray-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
                        lineNumber: 40,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Carregando..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
                        lineNumber: 41,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
                lineNumber: 39,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
            lineNumber: 38,
            columnNumber: 7
        }, this);
    }
    if (!user && !forceShowContent) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center bg-gray-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Redirecionando..."
                }, void 0, false, {
                    fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
                    lineNumber: 51,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
                lineNumber: 50,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
            lineNumber: 49,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
function LoadingSpinner({ size = 'md' }) {
    const sizeClasses = {
        sm: 'h-4 w-4',
        md: 'h-8 w-8',
        lg: 'h-12 w-12'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center justify-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `animate-spin rounded-full border-b-2 border-primary-600 ${sizeClasses[size]}`
        }, void 0, false, {
            fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
            lineNumber: 70,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
function useAuthGuard(redirectTo = '/') {
    const { user, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!loading && !user) {
            router.push(redirectTo);
        }
    }, [
        user,
        loading,
        router,
        redirectTo
    ]);
    return {
        user,
        loading,
        isAuthenticated: !!user
    };
}
}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "cn",
    ()=>cn,
    "formatCurrency",
    ()=>formatCurrency,
    "formatDate",
    ()=>formatDate,
    "generateSlug",
    ()=>generateSlug,
    "getAreaDisplayName",
    ()=>getAreaDisplayName
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date) {
    return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}
function formatCurrency(amount) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(amount);
}
function getAreaDisplayName(area) {
    const areaNames = {
        atendimento: 'Atendimento',
        planejamento: 'Planejamento',
        midia: 'Mídia'
    };
    return areaNames[area] || area;
}
function generateSlug(text) {
    return text.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').trim();
}
}),
"[project]/src/components/dashboard/DashboardLayout.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>DashboardLayout
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$Bars3Icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bars3Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js [app-ssr] (ecmascript) <export default as Bars3Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$XMarkIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__XMarkIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js [app-ssr] (ecmascript) <export default as XMarkIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$HomeIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HomeIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/HomeIcon.js [app-ssr] (ecmascript) <export default as HomeIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$FolderIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FolderIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/FolderIcon.js [app-ssr] (ecmascript) <export default as FolderIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$UserGroupIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserGroupIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js [app-ssr] (ecmascript) <export default as UserGroupIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$DocumentChartBarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DocumentChartBarIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js [app-ssr] (ecmascript) <export default as DocumentChartBarIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChatBubbleLeftRightIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChatBubbleLeftRightIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js [app-ssr] (ecmascript) <export default as ChatBubbleLeftRightIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$Cog6ToothIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Cog6ToothIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js [app-ssr] (ecmascript) <export default as Cog6ToothIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ArrowRightOnRectangleIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRightOnRectangleIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js [app-ssr] (ecmascript) <export default as ArrowRightOnRectangleIcon>");
'use client';
;
;
;
;
;
;
;
const navigation = [
    {
        name: 'Dashboard',
        href: '/dashboard',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$HomeIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HomeIcon$3e$__["HomeIcon"]
    },
    {
        name: 'Campanhas',
        href: '/dashboard/campanhas',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$FolderIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FolderIcon$3e$__["FolderIcon"]
    },
    {
        name: 'Agentes',
        href: '/dashboard/agentes',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$UserGroupIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserGroupIcon$3e$__["UserGroupIcon"]
    },
    {
        name: 'Relatórios',
        href: '/dashboard/relatorios',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$DocumentChartBarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DocumentChartBarIcon$3e$__["DocumentChartBarIcon"]
    },
    {
        name: 'Chat',
        href: '/dashboard/chat',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChatBubbleLeftRightIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChatBubbleLeftRightIcon$3e$__["ChatBubbleLeftRightIcon"]
    },
    {
        name: 'Configurações',
        href: '/dashboard/configuracoes',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$Cog6ToothIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Cog6ToothIcon$3e$__["Cog6ToothIcon"]
    }
];
function DashboardLayout({ children }) {
    const [sidebarOpen, setSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const { user, profile, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const handleSignOut = async ()=>{
        await signOut();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            sidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 z-50 lg:hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed inset-0 bg-gray-600 bg-opacity-75",
                        onClick: ()=>setSidebarOpen(false)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                        lineNumber: 47,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white shadow-xl",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex h-16 items-center justify-between px-6 border-b border-gray-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/dashboard",
                                        className: "text-xl font-bold text-primary-600",
                                        children: "AgentePub"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                        lineNumber: 50,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setSidebarOpen(false),
                                        className: "text-gray-400 hover:text-gray-600",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$XMarkIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__XMarkIcon$3e$__["XMarkIcon"], {
                                            className: "h-6 w-6"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                            lineNumber: 57,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                        lineNumber: 53,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                lineNumber: 49,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                className: "flex-1 px-6 py-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "space-y-2",
                                    children: navigation.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: item.href,
                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors', pathname === item.href ? 'bg-primary-100 text-primary-700' : 'text-gray-700 hover:bg-gray-100'),
                                                onClick: ()=>setSidebarOpen(false),
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(item.icon, {
                                                        className: "h-5 w-5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                        lineNumber: 75,
                                                        columnNumber: 23
                                                    }, this),
                                                    item.name
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                lineNumber: 65,
                                                columnNumber: 21
                                            }, this)
                                        }, item.name, false, {
                                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                            lineNumber: 64,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                    lineNumber: 62,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                lineNumber: 61,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-t border-gray-200 p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-3 mb-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-sm font-medium text-primary-700",
                                                    children: profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                    lineNumber: 86,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                lineNumber: 85,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex-1 min-w-0",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm font-medium text-gray-900 truncate",
                                                        children: profile?.full_name || user?.email
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                        lineNumber: 91,
                                                        columnNumber: 19
                                                    }, this),
                                                    profile?.area && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-xs text-gray-500 capitalize",
                                                        children: profile.area
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                        lineNumber: 95,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                lineNumber: 90,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                        lineNumber: 84,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleSignOut,
                                        className: "flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ArrowRightOnRectangleIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRightOnRectangleIcon$3e$__["ArrowRightOnRectangleIcon"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                lineNumber: 103,
                                                columnNumber: 17
                                            }, this),
                                            "Sair"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                        lineNumber: 99,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                lineNumber: 83,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                lineNumber: 46,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col flex-1 bg-white border-r border-gray-200",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex h-16 items-center px-6 border-b border-gray-200",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/dashboard",
                                className: "text-xl font-bold text-primary-600",
                                children: "AgentePub"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                lineNumber: 115,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                            lineNumber: 114,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "flex-1 px-6 py-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                className: "space-y-2",
                                children: navigation.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.href,
                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors', pathname === item.href ? 'bg-primary-100 text-primary-700' : 'text-gray-700 hover:bg-gray-100'),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(item.icon, {
                                                    className: "h-5 w-5"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                    lineNumber: 133,
                                                    columnNumber: 21
                                                }, this),
                                                item.name
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                            lineNumber: 124,
                                            columnNumber: 19
                                        }, this)
                                    }, item.name, false, {
                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                        lineNumber: 123,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                            lineNumber: 120,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "border-t border-gray-200 p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-3 mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm font-medium text-primary-700",
                                                children: profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                lineNumber: 144,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                            lineNumber: 143,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-1 min-w-0",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm font-medium text-gray-900 truncate",
                                                    children: profile?.full_name || user?.email
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                    lineNumber: 149,
                                                    columnNumber: 17
                                                }, this),
                                                profile?.area && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-xs text-gray-500 capitalize",
                                                    children: profile.area
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                                    lineNumber: 153,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                    lineNumber: 142,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleSignOut,
                                    className: "flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ArrowRightOnRectangleIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRightOnRectangleIcon$3e$__["ArrowRightOnRectangleIcon"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                            lineNumber: 161,
                                            columnNumber: 15
                                        }, this),
                                        "Sair"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                    lineNumber: 157,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                            lineNumber: 141,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                    lineNumber: 113,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                lineNumber: 112,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "lg:pl-64",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "sticky top-0 z-40 bg-white border-b border-gray-200 px-6 py-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setSidebarOpen(true),
                                    className: "lg:hidden text-gray-500 hover:text-gray-700",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$Bars3Icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bars3Icon$3e$__["Bars3Icon"], {
                                        className: "h-6 w-6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                        lineNumber: 177,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                    lineNumber: 173,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-gray-500",
                                        children: new Date().toLocaleDateString('pt-BR', {
                                            weekday: 'long',
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric'
                                        })
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                        lineNumber: 181,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                                    lineNumber: 180,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                            lineNumber: 172,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                        lineNumber: 171,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        className: "px-6 py-8",
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                        lineNumber: 194,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
                lineNumber: 169,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/dashboard/DashboardLayout.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/lib/agents/responseEngine.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Enhanced Response Generation Engine
 *
 * This module provides sophisticated response generation capabilities for AI agents,
 * transforming basic inputs into comprehensive, detailed, and contextually relevant responses.
 */ __turbopack_context__.s([
    "ResponseEngine",
    ()=>ResponseEngine
]);
class ResponseEngine {
    static instance;
    constructor(){}
    static getInstance() {
        if (!ResponseEngine.instance) {
            ResponseEngine.instance = new ResponseEngine();
        }
        return ResponseEngine.instance;
    }
    /**
   * Analyzes user input to extract meaningful information and context
   */ analyzeInput(input) {
        const words = input.toLowerCase().split(/\s+/);
        const sentences = input.split(/[.!?]+/).filter((s)=>s.trim().length > 0);
        // Extract keywords (simplified - in production would use NLP)
        const keywords = this.extractKeywords(input);
        // Extract entities (clients, dates, budgets, etc.)
        const entities = this.extractEntities(input);
        // Determine sentiment
        const sentiment = this.analyzeSentiment(input);
        // Assess complexity based on length, technical terms, and structure
        const complexity = this.assessComplexity(input, keywords);
        // Determine intent
        const intent = this.determineIntent(input, keywords);
        // Extract context
        const context = this.extractContext(input, entities);
        // Assess urgency
        const urgency = this.assessUrgency(input);
        // Calculate completeness
        const completeness = this.calculateCompleteness(input, entities);
        return {
            keywords,
            entities,
            sentiment,
            complexity,
            intent,
            context,
            urgency,
            completeness
        };
    }
    /**
   * Generates enhanced response based on analysis and agent type
   */ generateResponse(agentId, input, analysis) {
        const sections = [];
        // Generate core analysis section
        sections.push(this.generateAnalysisSection(analysis, input));
        // Generate agent-specific sections
        sections.push(...this.generateAgentSpecificSections(agentId, analysis, input));
        // Generate recommendations
        sections.push(this.generateRecommendationsSection(agentId, analysis));
        // Generate examples if appropriate
        if (analysis.complexity !== 'basic') {
            sections.push(this.generateExamplesSection(agentId, analysis));
        }
        // Generate warnings and considerations
        sections.push(this.generateWarningsSection(agentId, analysis));
        // Generate next steps
        sections.push(this.generateNextStepsSection(agentId, analysis));
        // Generate best practices
        sections.push(this.generateBestPracticesSection(agentId, analysis));
        // Sort sections by priority
        sections.sort((a, b)=>b.priority - a.priority);
        // Generate summary
        const summary = this.generateSummary(sections, analysis);
        // Generate follow-up questions
        const followUpQuestions = this.generateFollowUpQuestions(agentId, analysis);
        // Generate related topics
        const relatedTopics = this.generateRelatedTopics(agentId, analysis);
        // Generate action items
        const actionItems = this.extractActionItems(sections);
        // Calculate metrics
        const confidence = this.calculateConfidence(analysis, sections);
        const completeness = this.calculateResponseCompleteness(sections, analysis);
        const estimatedReadTime = this.calculateReadTime(sections);
        return {
            summary,
            sections,
            confidence,
            completeness,
            followUpQuestions,
            relatedTopics,
            estimatedReadTime,
            actionItems
        };
    }
    extractKeywords(input) {
        const commonWords = new Set([
            'o',
            'a',
            'de',
            'para',
            'com',
            'em',
            'um',
            'uma',
            'do',
            'da',
            'no',
            'na',
            'por',
            'se',
            'que',
            'como',
            'mais',
            'mas',
            'ou',
            'e',
            'é',
            'são',
            'foi',
            'ser',
            'ter',
            'seu',
            'sua',
            'seus',
            'suas'
        ]);
        const words = input.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/).filter((word)=>word.length > 2 && !commonWords.has(word));
        // Count frequency and return most common
        const frequency = {};
        words.forEach((word)=>{
            frequency[word] = (frequency[word] || 0) + 1;
        });
        return Object.entries(frequency).sort(([, a], [, b])=>b - a).slice(0, 10).map(([word])=>word);
    }
    extractEntities(input) {
        const entities = [];
        // Extract monetary values
        const moneyRegex = /R\$\s*[\d.,]+|[\d.,]+\s*reais?/gi;
        const moneyMatches = input.match(moneyRegex);
        if (moneyMatches) entities.push(...moneyMatches);
        // Extract dates
        const dateRegex = /\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}\s+de\s+\w+|\w+\s+\d{1,2}/gi;
        const dateMatches = input.match(dateRegex);
        if (dateMatches) entities.push(...dateMatches);
        // Extract percentages
        const percentRegex = /\d+%/g;
        const percentMatches = input.match(percentRegex);
        if (percentMatches) entities.push(...percentMatches);
        // Extract company/brand names (capitalized words)
        const brandRegex = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g;
        const brandMatches = input.match(brandRegex);
        if (brandMatches) entities.push(...brandMatches.slice(0, 5)); // Limit to avoid noise
        return [
            ...new Set(entities)
        ] // Remove duplicates
        ;
    }
    analyzeSentiment(input) {
        const positiveWords = [
            'bom',
            'ótimo',
            'excelente',
            'perfeito',
            'sucesso',
            'aprovado',
            'satisfeito',
            'feliz',
            'positivo'
        ];
        const negativeWords = [
            'ruim',
            'péssimo',
            'problema',
            'erro',
            'falha',
            'rejeitado',
            'insatisfeito',
            'preocupado',
            'urgente',
            'crítico'
        ];
        const words = input.toLowerCase().split(/\s+/);
        let positiveScore = 0;
        let negativeScore = 0;
        words.forEach((word)=>{
            if (positiveWords.some((pw)=>word.includes(pw))) positiveScore++;
            if (negativeWords.some((nw)=>word.includes(nw))) negativeScore++;
        });
        if (positiveScore > negativeScore) return 'positive';
        if (negativeScore > positiveScore) return 'negative';
        return 'neutral';
    }
    assessComplexity(input, keywords) {
        const technicalTerms = [
            'roi',
            'ctr',
            'cpm',
            'cpc',
            'kpi',
            'segmentação',
            'targeting',
            'remarketing',
            'lookalike',
            'funil',
            'conversão'
        ];
        const complexityIndicators = keywords.filter((k)=>technicalTerms.includes(k.toLowerCase())).length;
        if (input.length < 100 && complexityIndicators === 0) return 'basic';
        if (input.length > 300 || complexityIndicators > 2) return 'advanced';
        return 'intermediate';
    }
    determineIntent(input, keywords) {
        const intentPatterns = {
            'create_briefing': [
                'briefing',
                'criar',
                'novo',
                'campanha'
            ],
            'analyze_performance': [
                'análise',
                'performance',
                'resultado',
                'métricas'
            ],
            'plan_campaign': [
                'planejar',
                'cronograma',
                'estratégia',
                'planejamento'
            ],
            'optimize': [
                'otimizar',
                'melhorar',
                'ajustar',
                'otimização'
            ],
            'report': [
                'relatório',
                'report',
                'resumo',
                'status'
            ],
            'troubleshoot': [
                'problema',
                'erro',
                'não funciona',
                'ajuda'
            ]
        };
        const inputLower = input.toLowerCase();
        for (const [intent, patterns] of Object.entries(intentPatterns)){
            if (patterns.some((pattern)=>inputLower.includes(pattern))) {
                return intent;
            }
        }
        return 'general_inquiry';
    }
    extractContext(input, entities) {
        const context = {};
        // Extract budget information
        const budgetMatch = input.match(/(?:budget|orçamento).*?R\$\s*([\d.,]+)/i);
        if (budgetMatch) context.budget = budgetMatch[1];
        // Extract timeline information
        const timelineMatch = input.match(/(\d+)\s*(?:dias?|semanas?|meses?)/i);
        if (timelineMatch) context.timeline = timelineMatch[0];
        // Extract target audience
        const audienceMatch = input.match(/(?:público|target|audiência).*?(\d+[-–]\d+\s*anos?)/i);
        if (audienceMatch) context.targetAge = audienceMatch[1];
        // Extract channels
        const channels = [
            'instagram',
            'facebook',
            'google',
            'youtube',
            'linkedin',
            'tiktok'
        ];
        const mentionedChannels = channels.filter((channel)=>input.toLowerCase().includes(channel));
        if (mentionedChannels.length > 0) context.channels = mentionedChannels;
        return context;
    }
    assessUrgency(input) {
        const urgentWords = [
            'urgente',
            'imediato',
            'hoje',
            'agora',
            'crítico',
            'emergência'
        ];
        const mediumWords = [
            'breve',
            'logo',
            'próximo',
            'semana'
        ];
        const inputLower = input.toLowerCase();
        if (urgentWords.some((word)=>inputLower.includes(word))) return 'high';
        if (mediumWords.some((word)=>inputLower.includes(word))) return 'medium';
        return 'low';
    }
    calculateCompleteness(input, entities) {
        let score = 0;
        const maxScore = 10;
        // Length factor
        if (input.length > 50) score += 2;
        if (input.length > 150) score += 2;
        // Entity factor
        score += Math.min(entities.length, 3);
        // Structure factor
        if (input.includes('\n') || input.includes('-') || input.includes('•')) score += 1;
        // Question factor
        if (input.includes('?')) score += 1;
        // Context factor
        if (input.toLowerCase().includes('cliente') || input.toLowerCase().includes('campanha')) score += 1;
        return Math.min(score / maxScore, 1);
    }
    // Additional helper methods will be implemented in the next part...
    generateAnalysisSection(analysis, input) {
        return {
            title: "📊 Análise do Input",
            content: `**Complexidade:** ${analysis.complexity}\n**Intenção:** ${analysis.intent}\n**Urgência:** ${analysis.urgency}\n**Completude:** ${Math.round(analysis.completeness * 100)}%`,
            type: 'analysis',
            priority: 10
        };
    }
    generateAgentSpecificSections(agentId, analysis, input) {
        // This will be expanded with agent-specific logic
        return [];
    }
    generateRecommendationsSection(agentId, analysis) {
        return {
            title: "💡 Recomendações",
            content: "Recomendações baseadas na análise...",
            type: 'recommendation',
            priority: 9
        };
    }
    generateExamplesSection(agentId, analysis) {
        return {
            title: "📝 Exemplos",
            content: "Exemplos práticos...",
            type: 'example',
            priority: 7
        };
    }
    generateWarningsSection(agentId, analysis) {
        return {
            title: "⚠️ Considerações",
            content: "Pontos de atenção...",
            type: 'warning',
            priority: 8
        };
    }
    generateNextStepsSection(agentId, analysis) {
        return {
            title: "🎯 Próximos Passos",
            content: "Ações recomendadas...",
            type: 'next-steps',
            priority: 9
        };
    }
    generateBestPracticesSection(agentId, analysis) {
        return {
            title: "✅ Melhores Práticas",
            content: "Práticas recomendadas...",
            type: 'best-practices',
            priority: 6
        };
    }
    generateSummary(sections, analysis) {
        const mainInsights = [];
        // Extract key insights from analysis
        if (analysis.complexity === 'advanced') {
            mainInsights.push('Projeto de alta complexidade identificado');
        }
        if (analysis.urgency === 'high') {
            mainInsights.push('Demanda urgente requer atenção imediata');
        }
        if (analysis.completeness < 0.6) {
            mainInsights.push('Briefing necessita informações adicionais');
        }
        if (analysis.context.budget) {
            const budgetValue = parseFloat(analysis.context.budget.replace(/[^\d]/g, ''));
            if (budgetValue > 100000) {
                mainInsights.push('Budget robusto permite estratégia abrangente');
            } else if (budgetValue < 10000) {
                mainInsights.push('Budget limitado requer foco estratégico');
            }
        }
        if (analysis.riskFactors && analysis.riskFactors.length > 0) {
            mainInsights.push(`${analysis.riskFactors.length} fatores de risco identificados`);
        }
        if (analysis.opportunityIndicators && analysis.opportunityIndicators.length > 0) {
            mainInsights.push(`${analysis.opportunityIndicators.length} oportunidades detectadas`);
        }
        // Generate summary based on insights
        let summary = 'Análise completa do briefing realizada';
        if (mainInsights.length > 0) {
            summary += ': ' + mainInsights.join(', ').toLowerCase();
        }
        summary += `. ${sections.length} seções de recomendações geradas com foco em execução prática e resultados mensuráveis.`;
        return summary;
    }
    generateFollowUpQuestions(agentId, analysis) {
        const questions = [];
        // Questions based on missing information
        if (!analysis.context.budget) {
            questions.push('Qual o orçamento disponível para este projeto?');
        }
        if (!analysis.context.timeline) {
            questions.push('Qual o prazo desejado para conclusão?');
        }
        if (!analysis.context.targetAge && !analysis.keywords.includes('público')) {
            questions.push('Quem é o público-alvo principal desta campanha?');
        }
        if (!analysis.context.channels) {
            questions.push('Quais canais de mídia são preferenciais?');
        }
        // Agent-specific questions
        if (agentId === 'atendimento') {
            if (analysis.completeness < 0.7) {
                questions.push('Há alguma restrição ou requisito específico do cliente?');
            }
            questions.push('Qual o processo de aprovação interno do cliente?');
        } else if (agentId === 'planejamento') {
            questions.push('Existem campanhas anteriores que possam servir de benchmark?');
            questions.push('Quais são os principais concorrentes a serem considerados?');
        } else if (agentId === 'midia') {
            questions.push('Quais métricas são mais importantes para o sucesso?');
            questions.push('Há histórico de performance em canais similares?');
        }
        return questions.slice(0, 4) // Limit to 4 questions
        ;
    }
    generateRelatedTopics(agentId, analysis) {
        const topics = [];
        // Topics based on keywords and context
        if (analysis.keywords.includes('campanha')) {
            topics.push('Estratégia de Campanhas', 'Gestão de Budget', 'Métricas de Performance');
        }
        if (analysis.keywords.includes('digital') || analysis.context.channels) {
            topics.push('Marketing Digital', 'Attribution Modeling', 'Otimização de Conversão');
        }
        if (analysis.context.budget) {
            topics.push('ROI e Performance', 'Distribuição de Investimento');
        }
        // Agent-specific topics
        if (agentId === 'atendimento') {
            topics.push('Gestão de Cliente', 'Processos de Aprovação', 'Comunicação Estratégica');
        } else if (agentId === 'planejamento') {
            topics.push('Planejamento Estratégico', 'Análise de Mercado', 'Cronogramas de Projeto');
        } else if (agentId === 'midia') {
            topics.push('Otimização de Mídia', 'Analytics e Tracking', 'Testes A/B');
        }
        return [
            ...new Set(topics)
        ].slice(0, 5) // Remove duplicates and limit to 5
        ;
    }
    extractActionItems(sections) {
        const actionItems = [];
        // Extract action items from sections content
        sections.forEach((section)=>{
            if (section.type === 'next-steps' || section.type === 'recommendation') {
                const lines = section.content.split('\n');
                lines.forEach((line)=>{
                    // Look for bullet points or numbered items that sound like actions
                    if (line.match(/^[•\-\d\.]\s*/) && (line.includes('definir') || line.includes('criar') || line.includes('implementar') || line.includes('validar') || line.includes('estabelecer') || line.includes('configurar'))) {
                        const cleanLine = line.replace(/^[•\-\d\.\s]*/, '').trim();
                        if (cleanLine.length > 10) {
                            actionItems.push(cleanLine);
                        }
                    }
                });
            }
        });
        // Add default action items if none found
        if (actionItems.length === 0) {
            actionItems.push('Validar briefing com cliente', 'Definir cronograma detalhado', 'Estabelecer marcos de aprovação', 'Iniciar desenvolvimento da estratégia');
        }
        return actionItems.slice(0, 6) // Limit to 6 action items
        ;
    }
    calculateConfidence(analysis, sections) {
        return 0.85 // Placeholder
        ;
    }
    calculateResponseCompleteness(sections, analysis) {
        return 0.90 // Placeholder
        ;
    }
    calculateReadTime(sections) {
        const totalWords = sections.reduce((acc, section)=>acc + section.content.split(' ').length, 0);
        return Math.ceil(totalWords / 200) // Assuming 200 words per minute
        ;
    }
}
}),
"[project]/src/lib/agents/intelligenceLayer.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Input Analysis and Intelligence Layer
 * 
 * This module provides advanced input analysis capabilities including
 * sentiment analysis, keyword extraction, intent recognition, and context understanding.
 */ __turbopack_context__.s([
    "IntelligenceLayer",
    ()=>IntelligenceLayer
]);
class IntelligenceLayer {
    static instance;
    // Portuguese marketing/business vocabulary
    marketingTerms = new Set([
        'campanha',
        'marketing',
        'publicidade',
        'propaganda',
        'branding',
        'brand',
        'roi',
        'ctr',
        'cpm',
        'cpc',
        'impressões',
        'cliques',
        'conversões',
        'segmentação',
        'targeting',
        'remarketing',
        'lookalike',
        'funil',
        'awareness',
        'consideração',
        'conversão',
        'retenção',
        'advocacy'
    ]);
    businessTerms = new Set([
        'cliente',
        'negócio',
        'empresa',
        'startup',
        'corporação',
        'b2b',
        'b2c',
        'receita',
        'faturamento',
        'lucro',
        'margem',
        'investimento',
        'capital',
        'mercado',
        'concorrência',
        'competidor',
        'nicho',
        'segmento'
    ]);
    technicalTerms = new Set([
        'api',
        'crm',
        'erp',
        'analytics',
        'dashboard',
        'kpi',
        'métrica',
        'automação',
        'integração',
        'plataforma',
        'sistema',
        'ferramenta',
        'pixel',
        'tag',
        'tracking',
        'attribution',
        'attribution modeling'
    ]);
    urgencyIndicators = new Set([
        'urgente',
        'imediato',
        'hoje',
        'agora',
        'já',
        'rápido',
        'asap',
        'emergência',
        'crítico',
        'prioritário',
        'ontem',
        'deadline'
    ]);
    positiveIndicators = new Set([
        'ótimo',
        'excelente',
        'perfeito',
        'maravilhoso',
        'fantástico',
        'sucesso',
        'aprovado',
        'satisfeito',
        'feliz',
        'positivo',
        'bom'
    ]);
    negativeIndicators = new Set([
        'problema',
        'erro',
        'falha',
        'ruim',
        'péssimo',
        'insatisfeito',
        'preocupado',
        'crítico',
        'negativo',
        'rejeitado',
        'cancelado'
    ]);
    constructor(){}
    static getInstance() {
        if (!IntelligenceLayer.instance) {
            IntelligenceLayer.instance = new IntelligenceLayer();
        }
        return IntelligenceLayer.instance;
    }
    performAdvancedAnalysis(input) {
        // Basic analysis first
        const basicAnalysis = this.performBasicAnalysis(input);
        // Advanced analysis
        const namedEntities = this.extractNamedEntities(input);
        const topicClusters = this.identifyTopicClusters(input);
        const linguisticFeatures = this.analyzeLinguisticFeatures(input);
        const businessContext = this.inferBusinessContext(input, namedEntities, topicClusters);
        const riskFactors = this.identifyRiskFactors(input, basicAnalysis, businessContext);
        const opportunityIndicators = this.identifyOpportunities(input, basicAnalysis, businessContext);
        return {
            ...basicAnalysis,
            namedEntities,
            topicClusters,
            linguisticFeatures,
            businessContext,
            riskFactors,
            opportunityIndicators
        };
    }
    performBasicAnalysis(input) {
        const words = input.toLowerCase().split(/\s+/);
        const sentences = input.split(/[.!?]+/).filter((s)=>s.trim().length > 0);
        return {
            keywords: this.extractKeywords(input),
            entities: this.extractBasicEntities(input),
            sentiment: this.analyzeSentiment(input),
            complexity: this.assessComplexity(input),
            intent: this.determineIntent(input),
            context: this.extractContext(input),
            urgency: this.assessUrgency(input),
            completeness: this.calculateCompleteness(input)
        };
    }
    extractNamedEntities(input) {
        const entities = [];
        // Money entities
        const moneyRegex = /R\$\s*([\d.,]+)|(\d+(?:\.\d{3})*(?:,\d{2})?)\s*reais?/gi;
        let match;
        while((match = moneyRegex.exec(input)) !== null){
            entities.push({
                text: match[0],
                type: 'MONEY',
                confidence: 0.9,
                startIndex: match.index,
                endIndex: match.index + match[0].length
            });
        }
        // Percentage entities
        const percentRegex = /(\d+(?:,\d+)?)\s*%/g;
        while((match = percentRegex.exec(input)) !== null){
            entities.push({
                text: match[0],
                type: 'PERCENTAGE',
                confidence: 0.95,
                startIndex: match.index,
                endIndex: match.index + match[0].length
            });
        }
        // Date entities
        const dateRegex = /(\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}\s+de\s+\w+|\w+\s+\d{1,2})/gi;
        while((match = dateRegex.exec(input)) !== null){
            entities.push({
                text: match[0],
                type: 'DATE',
                confidence: 0.8,
                startIndex: match.index,
                endIndex: match.index + match[0].length
            });
        }
        // Organization entities (capitalized sequences)
        const orgRegex = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g;
        while((match = orgRegex.exec(input)) !== null){
            // Filter out common words that might be capitalized
            const commonWords = [
                'Instagram',
                'Facebook',
                'Google',
                'YouTube',
                'LinkedIn',
                'TikTok'
            ];
            if (match[0].length > 3 && !commonWords.includes(match[0])) {
                entities.push({
                    text: match[0],
                    type: 'ORGANIZATION',
                    confidence: 0.6,
                    startIndex: match.index,
                    endIndex: match.index + match[0].length
                });
            }
        }
        return entities;
    }
    identifyTopicClusters(input) {
        const words = input.toLowerCase().split(/\s+/);
        const clusters = [];
        // Marketing cluster
        const marketingWords = words.filter((word)=>this.marketingTerms.has(word));
        if (marketingWords.length > 0) {
            clusters.push({
                name: 'Marketing Digital',
                keywords: marketingWords,
                relevance: marketingWords.length / words.length,
                category: 'marketing'
            });
        }
        // Business cluster
        const businessWords = words.filter((word)=>this.businessTerms.has(word));
        if (businessWords.length > 0) {
            clusters.push({
                name: 'Negócios',
                keywords: businessWords,
                relevance: businessWords.length / words.length,
                category: 'business'
            });
        }
        // Technical cluster
        const technicalWords = words.filter((word)=>this.technicalTerms.has(word));
        if (technicalWords.length > 0) {
            clusters.push({
                name: 'Técnico',
                keywords: technicalWords,
                relevance: technicalWords.length / words.length,
                category: 'technical'
            });
        }
        return clusters.sort((a, b)=>b.relevance - a.relevance);
    }
    analyzeLinguisticFeatures(input) {
        const sentences = input.split(/[.!?]+/).filter((s)=>s.trim().length > 0);
        const words = input.split(/\s+/);
        // Count questions and imperatives
        const questionCount = (input.match(/\?/g) || []).length;
        const imperativeCount = this.countImperatives(input);
        // Calculate average sentence length
        const averageSentenceLength = sentences.length > 0 ? sentences.reduce((sum, s)=>sum + s.split(/\s+/).length, 0) / sentences.length : 0;
        // Assess formality
        const formalityLevel = this.assessFormality(input);
        // Technical complexity based on technical terms
        const technicalWords = words.filter((word)=>this.technicalTerms.has(word.toLowerCase()));
        const technicalComplexity = technicalWords.length / words.length;
        // Emotional intensity based on emotional words
        const emotionalWords = words.filter((word)=>this.positiveIndicators.has(word.toLowerCase()) || this.negativeIndicators.has(word.toLowerCase()));
        const emotionalIntensity = emotionalWords.length / words.length;
        // Simple readability score (based on sentence length and word complexity)
        const readabilityScore = this.calculateReadabilityScore(sentences, words);
        return {
            readabilityScore,
            formalityLevel,
            technicalComplexity,
            emotionalIntensity,
            questionCount,
            imperativeCount,
            averageSentenceLength
        };
    }
    inferBusinessContext(input, entities, clusters) {
        // Infer industry from topic clusters and keywords
        const industry = [];
        clusters.forEach((cluster)=>{
            if (cluster.category === 'marketing') industry.push('Marketing Digital');
            if (cluster.category === 'business') industry.push('Consultoria');
            if (cluster.category === 'technical') industry.push('Tecnologia');
        });
        // Infer budget range from money entities
        let budgetRange = 'small';
        const moneyEntities = entities.filter((e)=>e.type === 'MONEY');
        if (moneyEntities.length > 0) {
            const amounts = moneyEntities.map((e)=>{
                const numStr = e.text.replace(/[^\d,]/g, '').replace(',', '.');
                return parseFloat(numStr) || 0;
            });
            const maxAmount = Math.max(...amounts);
            if (maxAmount < 5000) budgetRange = 'micro';
            else if (maxAmount < 25000) budgetRange = 'small';
            else if (maxAmount < 100000) budgetRange = 'medium';
            else if (maxAmount < 500000) budgetRange = 'large';
            else budgetRange = 'enterprise';
        }
        // Infer business stage from language and complexity
        let businessStage = 'growth';
        const words = input.toLowerCase();
        if (words.includes('startup') || words.includes('começando')) businessStage = 'startup';
        else if (words.includes('empresa') || words.includes('corporação')) businessStage = 'mature';
        else if (words.includes('multinacional') || words.includes('holding')) businessStage = 'enterprise';
        // Infer marketing maturity from technical terms usage
        const technicalCluster = clusters.find((c)=>c.category === 'technical');
        let marketingMaturity = 'intermediate';
        if (!technicalCluster || technicalCluster.relevance < 0.1) marketingMaturity = 'beginner';
        else if (technicalCluster.relevance > 0.3) marketingMaturity = 'advanced';
        // Infer timeframe from urgency and date entities
        let timeframe = 'medium-term';
        const urgencyWords = input.toLowerCase().split(/\s+/).filter((word)=>this.urgencyIndicators.has(word));
        if (urgencyWords.length > 0) timeframe = 'immediate';
        else if (entities.some((e)=>e.type === 'DATE')) timeframe = 'short-term';
        return {
            industry,
            businessStage,
            marketingMaturity,
            budgetRange,
            timeframe
        };
    }
    identifyRiskFactors(input, analysis, context) {
        const risks = [];
        // Timeline risks
        if (analysis.urgency === 'high' && analysis.complexity === 'advanced') {
            risks.push({
                type: 'timeline',
                description: 'Projeto complexo com prazo apertado pode comprometer qualidade',
                severity: 'high',
                probability: 0.8,
                impact: 0.9,
                mitigation: [
                    'Alocar recursos adicionais',
                    'Simplificar escopo inicial',
                    'Estabelecer marcos intermediários'
                ]
            });
        }
        // Budget risks
        if (context.budgetRange === 'micro' && analysis.complexity === 'advanced') {
            risks.push({
                type: 'budget',
                description: 'Orçamento limitado para projeto complexo',
                severity: 'medium',
                probability: 0.7,
                impact: 0.8,
                mitigation: [
                    'Redefinir escopo',
                    'Implementação em fases',
                    'Buscar orçamento adicional'
                ]
            });
        }
        // Communication risks
        if (analysis.completeness < 0.5) {
            risks.push({
                type: 'communication',
                description: 'Briefing incompleto pode gerar mal-entendidos',
                severity: 'medium',
                probability: 0.6,
                impact: 0.7,
                mitigation: [
                    'Reunião de alinhamento urgente',
                    'Checklist de informações',
                    'Validação constante com cliente'
                ]
            });
        }
        return risks;
    }
    identifyOpportunities(input, analysis, context) {
        const opportunities = [];
        // Growth opportunities
        if (context.businessStage === 'startup' && context.budgetRange !== 'micro') {
            opportunities.push({
                type: 'growth',
                description: 'Startup com orçamento adequado tem potencial de crescimento acelerado',
                potential: 'high',
                effort: 'medium',
                timeframe: 'short'
            });
        }
        // Efficiency opportunities
        if (context.marketingMaturity === 'beginner' && analysis.complexity === 'basic') {
            opportunities.push({
                type: 'efficiency',
                description: 'Implementação de processos básicos pode gerar ganhos rápidos',
                potential: 'medium',
                effort: 'low',
                timeframe: 'immediate'
            });
        }
        // Innovation opportunities
        if (context.marketingMaturity === 'advanced' && analysis.keywords.includes('inovação')) {
            opportunities.push({
                type: 'innovation',
                description: 'Cliente avançado aberto a soluções inovadoras',
                potential: 'high',
                effort: 'high',
                timeframe: 'medium'
            });
        }
        return opportunities;
    }
    // Helper methods
    extractKeywords(input) {
        const commonWords = new Set([
            'o',
            'a',
            'de',
            'para',
            'com',
            'em',
            'um',
            'uma',
            'do',
            'da',
            'no',
            'na',
            'por',
            'se',
            'que',
            'como',
            'mais',
            'mas',
            'ou',
            'e',
            'é',
            'são',
            'foi',
            'ser',
            'ter',
            'seu',
            'sua',
            'seus',
            'suas'
        ]);
        const words = input.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/).filter((word)=>word.length > 2 && !commonWords.has(word));
        const frequency = {};
        words.forEach((word)=>{
            frequency[word] = (frequency[word] || 0) + 1;
        });
        return Object.entries(frequency).sort(([, a], [, b])=>b - a).slice(0, 10).map(([word])=>word);
    }
    extractBasicEntities(input) {
        const entities = [];
        const moneyRegex = /R\$\s*[\d.,]+|[\d.,]+\s*reais?/gi;
        const moneyMatches = input.match(moneyRegex);
        if (moneyMatches) entities.push(...moneyMatches);
        const dateRegex = /\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}\s+de\s+\w+|\w+\s+\d{1,2}/gi;
        const dateMatches = input.match(dateRegex);
        if (dateMatches) entities.push(...dateMatches);
        const percentRegex = /\d+%/g;
        const percentMatches = input.match(percentRegex);
        if (percentMatches) entities.push(...percentMatches);
        return [
            ...new Set(entities)
        ];
    }
    analyzeSentiment(input) {
        const words = input.toLowerCase().split(/\s+/);
        let positiveScore = 0;
        let negativeScore = 0;
        words.forEach((word)=>{
            if (this.positiveIndicators.has(word)) positiveScore++;
            if (this.negativeIndicators.has(word)) negativeScore++;
        });
        if (positiveScore > negativeScore) return 'positive';
        if (negativeScore > positiveScore) return 'negative';
        return 'neutral';
    }
    assessComplexity(input) {
        const words = input.toLowerCase().split(/\s+/);
        const technicalTermCount = words.filter((word)=>this.technicalTerms.has(word)).length;
        if (input.length < 100 && technicalTermCount === 0) return 'basic';
        if (input.length > 300 || technicalTermCount > 2) return 'advanced';
        return 'intermediate';
    }
    determineIntent(input) {
        const inputLower = input.toLowerCase();
        if (inputLower.includes('briefing') || inputLower.includes('criar')) return 'create_briefing';
        if (inputLower.includes('análise') || inputLower.includes('performance')) return 'analyze_performance';
        if (inputLower.includes('planejar') || inputLower.includes('estratégia')) return 'plan_campaign';
        if (inputLower.includes('otimizar') || inputLower.includes('melhorar')) return 'optimize';
        if (inputLower.includes('relatório') || inputLower.includes('report')) return 'report';
        if (inputLower.includes('problema') || inputLower.includes('erro')) return 'troubleshoot';
        return 'general_inquiry';
    }
    extractContext(input) {
        const context = {};
        const budgetMatch = input.match(/(?:budget|orçamento).*?R\$\s*([\d.,]+)/i);
        if (budgetMatch) context.budget = budgetMatch[1];
        const timelineMatch = input.match(/(\d+)\s*(?:dias?|semanas?|meses?)/i);
        if (timelineMatch) context.timeline = timelineMatch[0];
        const audienceMatch = input.match(/(?:público|target|audiência).*?(\d+[-–]\d+\s*anos?)/i);
        if (audienceMatch) context.targetAge = audienceMatch[1];
        const channels = [
            'instagram',
            'facebook',
            'google',
            'youtube',
            'linkedin',
            'tiktok'
        ];
        const mentionedChannels = channels.filter((channel)=>input.toLowerCase().includes(channel));
        if (mentionedChannels.length > 0) context.channels = mentionedChannels;
        return context;
    }
    assessUrgency(input) {
        const words = input.toLowerCase().split(/\s+/);
        const urgentWords = words.filter((word)=>this.urgencyIndicators.has(word));
        if (urgentWords.length > 0) return 'high';
        if (input.toLowerCase().includes('breve') || input.toLowerCase().includes('próximo')) return 'medium';
        return 'low';
    }
    calculateCompleteness(input) {
        let score = 0;
        const maxScore = 10;
        if (input.length > 50) score += 2;
        if (input.length > 150) score += 2;
        if (input.includes('R$') || input.toLowerCase().includes('orçamento')) score += 2;
        if (input.match(/\d+\s*(?:dias?|semanas?|meses?)/)) score += 2;
        if (input.toLowerCase().includes('público') || input.toLowerCase().includes('target')) score += 1;
        if (input.toLowerCase().includes('cliente') || input.toLowerCase().includes('campanha')) score += 1;
        return Math.min(score / maxScore, 1);
    }
    countImperatives(input) {
        const imperativePatterns = [
            /\b(?:faça|crie|desenvolva|implemente|execute|realize)\b/gi,
            /\b(?:preciso|quero|gostaria|solicito)\b/gi
        ];
        return imperativePatterns.reduce((count, pattern)=>{
            const matches = input.match(pattern);
            return count + (matches ? matches.length : 0);
        }, 0);
    }
    assessFormality(input) {
        const informalIndicators = [
            'oi',
            'olá',
            'beleza',
            'valeu',
            'obrigado',
            'brigado'
        ];
        const formalIndicators = [
            'prezado',
            'cordialmente',
            'atenciosamente',
            'solicito',
            'gostaria'
        ];
        const words = input.toLowerCase().split(/\s+/);
        const informalCount = words.filter((word)=>informalIndicators.includes(word)).length;
        const formalCount = words.filter((word)=>formalIndicators.includes(word)).length;
        if (formalCount > informalCount) return 'formal';
        if (informalCount > formalCount) return 'informal';
        return 'neutral';
    }
    calculateReadabilityScore(sentences, words) {
        if (sentences.length === 0 || words.length === 0) return 0;
        const avgSentenceLength = words.length / sentences.length;
        const complexWords = words.filter((word)=>word.length > 6).length;
        const complexWordRatio = complexWords / words.length;
        // Simple readability formula (higher score = easier to read)
        return Math.max(0, 100 - avgSentenceLength * 1.5 - complexWordRatio * 100);
    }
}
}),
"[project]/src/lib/agents/knowledgeBase.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Agent Knowledge Base and Specialization System
 * 
 * This module contains specialized knowledge, best practices, and response templates
 * for each agent type, enabling them to provide expert-level insights and recommendations.
 */ __turbopack_context__.s([
    "AgentKnowledgeBase",
    ()=>AgentKnowledgeBase
]);
class AgentKnowledgeBase {
    static instance;
    knowledgeMap = new Map();
    constructor(){
        this.initializeKnowledge();
    }
    static getInstance() {
        if (!AgentKnowledgeBase.instance) {
            AgentKnowledgeBase.instance = new AgentKnowledgeBase();
        }
        return AgentKnowledgeBase.instance;
    }
    getAgentKnowledge(agentId) {
        return this.knowledgeMap.get(agentId);
    }
    getApplicableScenarios(agentId, analysis) {
        const knowledge = this.knowledgeMap.get(agentId);
        if (!knowledge) return [];
        return knowledge.commonScenarios.filter((scenario)=>scenario.triggers.some((trigger)=>analysis.keywords.some((keyword)=>keyword.includes(trigger.toLowerCase())) || analysis.intent.includes(trigger.toLowerCase())));
    }
    getBestPractices(agentId, analysis) {
        const knowledge = this.knowledgeMap.get(agentId);
        if (!knowledge) return [];
        // Return relevant best practices based on context
        return knowledge.bestPractices.filter((practice)=>analysis.keywords.some((keyword)=>practice.category.toLowerCase().includes(keyword) || practice.title.toLowerCase().includes(keyword))).slice(0, 3) // Limit to top 3 most relevant
        ;
    }
    getInsights(agentId, analysis) {
        const knowledge = this.knowledgeMap.get(agentId);
        if (!knowledge) return [];
        return knowledge.industryInsights.filter((rule)=>rule.condition(analysis)).sort((a, b)=>{
            const priorityOrder = {
                high: 3,
                medium: 2,
                low: 1
            };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
    }
    getWarnings(agentId, analysis) {
        const knowledge = this.knowledgeMap.get(agentId);
        if (!knowledge) return [];
        return knowledge.warningTriggers.filter((trigger)=>trigger.condition(analysis));
    }
    getTroubleshootingGuides(agentId, analysis) {
        const knowledge = this.knowledgeMap.get(agentId);
        if (!knowledge) return [];
        // Return guides that match problem indicators in the input
        return knowledge.troubleshooting.filter((guide)=>guide.symptoms.some((symptom)=>analysis.keywords.some((keyword)=>keyword.includes(symptom.toLowerCase()))));
    }
    initializeKnowledge() {
        // Initialize Atendimento Agent Knowledge
        this.knowledgeMap.set('atendimento', {
            specializations: [
                'Gestão de Briefings',
                'Comunicação com Cliente',
                'Cronogramas de Aprovação',
                'Gestão de Expectativas',
                'Documentação de Projetos'
            ],
            commonScenarios: [
                {
                    id: 'briefing_incompleto',
                    name: 'Briefing Incompleto',
                    description: 'Cliente forneceu informações insuficientes para iniciar o projeto',
                    triggers: [
                        'briefing',
                        'incompleto',
                        'faltando',
                        'informação'
                    ],
                    response: 'Identifiquei que algumas informações essenciais estão faltando no briefing.',
                    examples: [
                        'Briefing sem definição de público-alvo',
                        'Orçamento não especificado',
                        'Prazo não definido'
                    ],
                    relatedTopics: [
                        'Coleta de Informações',
                        'Validação de Briefing',
                        'Comunicação com Cliente'
                    ]
                },
                {
                    id: 'prazo_apertado',
                    name: 'Prazo Apertado',
                    description: 'Cliente solicita entrega em prazo muito curto',
                    triggers: [
                        'urgente',
                        'rápido',
                        'hoje',
                        'amanhã',
                        'prazo'
                    ],
                    response: 'Detectei uma solicitação com prazo apertado que requer atenção especial.',
                    examples: [
                        'Campanha para lançar em 48h',
                        'Material para evento na próxima semana',
                        'Aprovação urgente necessária'
                    ],
                    relatedTopics: [
                        'Gestão de Cronograma',
                        'Priorização',
                        'Recursos Adicionais'
                    ]
                }
            ],
            bestPractices: [
                {
                    category: 'Briefing',
                    title: 'Estruturação Completa de Briefings',
                    description: 'Todo briefing deve conter informações essenciais para evitar retrabalho',
                    implementation: [
                        'Sempre validar objetivo principal',
                        'Confirmar público-alvo detalhado',
                        'Estabelecer orçamento claro',
                        'Definir cronograma realista',
                        'Documentar canais de aprovação'
                    ],
                    benefits: [
                        'Reduz retrabalho em 60%',
                        'Melhora satisfação do cliente',
                        'Acelera processo de aprovação',
                        'Diminui mal-entendidos'
                    ],
                    commonMistakes: [
                        'Assumir informações não explícitas',
                        'Não validar orçamento real',
                        'Ignorar restrições do cliente',
                        'Não documentar mudanças'
                    ]
                },
                {
                    category: 'Comunicação',
                    title: 'Gestão Proativa de Expectativas',
                    description: 'Manter cliente informado previne problemas e builds confiança',
                    implementation: [
                        'Updates semanais obrigatórios',
                        'Alertas antecipados sobre riscos',
                        'Documentação de todas as decisões',
                        'Canal direto para dúvidas'
                    ],
                    benefits: [
                        'Aumenta confiança do cliente',
                        'Reduz ansiedade e pressão',
                        'Facilita resolução de problemas',
                        'Melhora relacionamento de longo prazo'
                    ],
                    commonMistakes: [
                        'Comunicar apenas quando há problemas',
                        'Usar linguagem muito técnica',
                        'Não confirmar entendimento',
                        'Deixar cliente sem resposta'
                    ]
                }
            ],
            troubleshooting: [
                {
                    problem: 'Cliente não responde aprovações',
                    symptoms: [
                        'silêncio prolongado',
                        'aprovações pendentes',
                        'cronograma atrasado'
                    ],
                    causes: [
                        'Sobrecarga do cliente',
                        'Falta de clareza na solicitação',
                        'Processo de aprovação interno complexo',
                        'Insatisfação não comunicada'
                    ],
                    solutions: [
                        'Ligar diretamente para o cliente',
                        'Simplificar processo de aprovação',
                        'Oferecer reunião de alinhamento',
                        'Criar cronograma de follow-ups'
                    ],
                    prevention: [
                        'Estabelecer SLA de resposta',
                        'Mapear processo interno do cliente',
                        'Criar lembretes automáticos',
                        'Ter contato backup'
                    ]
                }
            ],
            templates: [
                {
                    name: 'Briefing Estruturado',
                    structure: [
                        {
                            title: '📋 Resumo Executivo',
                            contentGenerator: (analysis, input)=>this.generateExecutiveSummary(analysis, input),
                            priority: 10,
                            required: true
                        },
                        {
                            title: '🎯 Objetivos e Metas',
                            contentGenerator: (analysis, input)=>this.generateObjectives(analysis, input),
                            priority: 9,
                            required: true
                        },
                        {
                            title: '👥 Público-Alvo',
                            contentGenerator: (analysis, input)=>this.generateTargetAudience(analysis, input),
                            priority: 8,
                            required: true
                        }
                    ],
                    applicableWhen: (analysis)=>analysis.intent.includes('briefing') || analysis.keywords.includes('briefing')
                }
            ],
            industryInsights: [
                {
                    condition: (analysis)=>analysis.context.budget && parseFloat(analysis.context.budget.replace(/[^\d]/g, '')) < 10000,
                    insight: 'Para orçamentos menores, recomendo focar em 1-2 canais principais para maximizar impacto.',
                    actionable: true,
                    priority: 'high'
                },
                {
                    condition: (analysis)=>analysis.urgency === 'high',
                    insight: 'Projetos urgentes têm 40% mais chance de problemas. Considere recursos adicionais.',
                    actionable: true,
                    priority: 'high'
                }
            ],
            warningTriggers: [
                {
                    condition: (analysis)=>analysis.completeness < 0.5,
                    warning: 'Briefing incompleto detectado. Informações essenciais estão faltando.',
                    severity: 'important',
                    mitigation: [
                        'Solicitar reunião de alinhamento',
                        'Enviar checklist de informações necessárias',
                        'Agendar call de briefing completo'
                    ]
                },
                {
                    condition: (analysis)=>analysis.urgency === 'high' && !analysis.context.budget,
                    warning: 'Projeto urgente sem orçamento definido pode gerar problemas de execução.',
                    severity: 'critical',
                    mitigation: [
                        'Definir orçamento mínimo imediatamente',
                        'Estabelecer escopo reduzido',
                        'Comunicar riscos ao cliente'
                    ]
                }
            ]
        });
        // Initialize Planejamento Agent Knowledge
        this.knowledgeMap.set('planejamento', {
            specializations: [
                'Estratégia de Campanhas',
                'Cronogramas Inteligentes',
                'Análise de Concorrência',
                'Distribuição de Budget',
                'Otimização de Performance'
            ],
            commonScenarios: [
                {
                    id: 'campanha_multicanal',
                    name: 'Campanha Multi-canal',
                    description: 'Planejamento de campanha que utiliza múltiplos canais de mídia',
                    triggers: [
                        'multicanal',
                        'instagram',
                        'facebook',
                        'google',
                        'canais'
                    ],
                    response: 'Identificada necessidade de estratégia multi-canal integrada.',
                    examples: [
                        'Campanha usando Instagram + Google Ads',
                        'Estratégia omnichannel completa',
                        'Integração online e offline'
                    ],
                    relatedTopics: [
                        'Integração de Canais',
                        'Attribution Modeling',
                        'Budget Allocation'
                    ]
                }
            ],
            bestPractices: [
                {
                    category: 'Estratégia',
                    title: 'Planejamento Baseado em Dados',
                    description: 'Usar dados históricos e benchmarks para criar estratégias mais eficazes',
                    implementation: [
                        'Analisar performance histórica',
                        'Benchmarking com concorrentes',
                        'Definir KPIs específicos',
                        'Criar hipóteses testáveis'
                    ],
                    benefits: [
                        'Melhora ROI em até 35%',
                        'Reduz tempo de otimização',
                        'Aumenta previsibilidade',
                        'Facilita tomada de decisão'
                    ],
                    commonMistakes: [
                        'Ignorar dados históricos',
                        'Copiar estratégias sem contexto',
                        'Não definir métricas claras',
                        'Não testar hipóteses'
                    ]
                }
            ],
            troubleshooting: [],
            templates: [],
            industryInsights: [],
            warningTriggers: []
        });
        // Initialize Mídia Agent Knowledge
        this.knowledgeMap.set('midia', {
            specializations: [
                'Análise de Performance',
                'Otimização de Campanhas',
                'Relatórios Avançados',
                'Gestão de Budget',
                'Targeting e Segmentação'
            ],
            commonScenarios: [],
            bestPractices: [],
            troubleshooting: [],
            templates: [],
            industryInsights: [],
            warningTriggers: []
        });
    }
    // Template content generators
    generateExecutiveSummary(analysis, input) {
        return `**Projeto:** ${analysis.context.campaign || 'Campanha identificada no briefing'}
**Complexidade:** ${analysis.complexity}
**Prazo estimado:** ${analysis.context.timeline || '30 dias (recomendado)'}
**Status:** Briefing analisado e estruturado`;
    }
    generateObjectives(analysis, input) {
        const objectives = [];
        if (analysis.keywords.includes('lançamento')) objectives.push('• Lançamento de produto/serviço');
        if (analysis.keywords.includes('awareness')) objectives.push('• Aumentar conhecimento da marca');
        if (analysis.keywords.includes('vendas')) objectives.push('• Gerar vendas diretas');
        if (objectives.length === 0) objectives.push('• Objetivo principal identificado no briefing');
        return objectives.join('\n');
    }
    generateTargetAudience(analysis, input) {
        let audience = '• **Idade:** ';
        if (analysis.context.targetAge) {
            audience += analysis.context.targetAge;
        } else {
            audience += '25-45 anos (sugerido)';
        }
        audience += '\n• **Localização:** Principais centros urbanos';
        audience += '\n• **Comportamento:** Ativo em redes sociais';
        return audience;
    }
}
}),
"[project]/src/lib/agents/responseTemplates.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Context-Aware Response Templates System
 *
 * This module provides dynamic response templates that adapt based on input analysis,
 * user context, and agent specialization to generate comprehensive, structured responses.
 */ __turbopack_context__.s([
    "ResponseTemplateEngine",
    ()=>ResponseTemplateEngine
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$knowledgeBase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents/knowledgeBase.ts [app-ssr] (ecmascript)");
;
class ResponseTemplateEngine {
    static instance;
    templates = new Map();
    knowledgeBase;
    constructor(){
        this.knowledgeBase = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$knowledgeBase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AgentKnowledgeBase"].getInstance();
        this.initializeTemplates();
    }
    static getInstance() {
        if (!ResponseTemplateEngine.instance) {
            ResponseTemplateEngine.instance = new ResponseTemplateEngine();
        }
        return ResponseTemplateEngine.instance;
    }
    generateResponse(context) {
        console.log(`Generating response for agent: ${context.agentId}`);
        const agentTemplates = this.templates.get(context.agentId) || [];
        console.log(`Found ${agentTemplates.length} templates for agent`);
        // Find applicable templates
        const applicableTemplates = agentTemplates.filter((template)=>{
            const applicable = template.applicableWhen(context);
            console.log(`Template ${template.name} applicable: ${applicable}`);
            return applicable;
        }).sort((a, b)=>b.priority - a.priority);
        console.log(`${applicableTemplates.length} applicable templates found`);
        // Use the highest priority applicable template
        const selectedTemplate = applicableTemplates[0];
        if (!selectedTemplate) {
            console.log('No applicable template found, using fallback');
            return this.generateFallbackResponse(context);
        }
        console.log(`Using template: ${selectedTemplate.name}`);
        // Generate sections from template
        const sections = [];
        for (const sectionGen of selectedTemplate.sections){
            // Check if section condition is met (if any)
            if (sectionGen.condition && !sectionGen.condition(context)) {
                console.log(`Skipping section ${sectionGen.id} - condition not met`);
                continue;
            }
            try {
                console.log(`Generating section: ${sectionGen.id}`);
                const section = sectionGen.generator(context);
                sections.push(section);
                console.log(`Section ${sectionGen.id} generated successfully`);
            } catch (error) {
                console.error(`Error generating section ${sectionGen.id}:`, error);
            // Continue with other sections
            }
        }
        console.log(`Generated ${sections.length} sections total`);
        // Sort sections by priority
        return sections.sort((a, b)=>b.priority - a.priority);
    }
    initializeTemplates() {
        // Atendimento Agent Templates
        this.templates.set('atendimento', [
            {
                id: 'comprehensive_briefing',
                name: 'Briefing Abrangente',
                description: 'Template completo para análise e estruturação de briefings',
                applicableWhen: (context)=>{
                    // Always apply for atendimento agent - this is the primary template
                    console.log('Checking briefing template applicability:', {
                        intent: context.analysis.intent,
                        keywords: context.analysis.keywords,
                        hasKeywords: context.analysis.keywords.length > 0
                    });
                    return true // Always applicable for atendimento
                    ;
                },
                priority: 10,
                sections: [
                    {
                        id: 'executive_summary',
                        title: '📋 Resumo Executivo',
                        type: 'analysis',
                        priority: 10,
                        required: true,
                        generator: (context)=>this.generateExecutiveSummary(context)
                    },
                    {
                        id: 'detailed_analysis',
                        title: '🔍 Análise Detalhada do Briefing',
                        type: 'analysis',
                        priority: 9,
                        required: true,
                        generator: (context)=>this.generateDetailedAnalysis(context)
                    },
                    {
                        id: 'missing_information',
                        title: '❓ Informações Necessárias',
                        type: 'warning',
                        priority: 8,
                        required: false,
                        condition: (context)=>context.analysis.completeness < 0.7,
                        generator: (context)=>this.generateMissingInformation(context)
                    },
                    {
                        id: 'strategic_recommendations',
                        title: '💡 Recomendações Estratégicas',
                        type: 'recommendation',
                        priority: 8,
                        required: true,
                        generator: (context)=>this.generateStrategicRecommendations(context)
                    },
                    {
                        id: 'timeline_breakdown',
                        title: '📅 Cronograma Detalhado',
                        type: 'detailed-guide',
                        priority: 7,
                        required: true,
                        generator: (context)=>this.generateTimelineBreakdown(context)
                    },
                    {
                        id: 'approval_process',
                        title: '✅ Processo de Aprovação',
                        type: 'detailed-guide',
                        priority: 6,
                        required: true,
                        generator: (context)=>this.generateApprovalProcess(context)
                    },
                    {
                        id: 'risk_assessment',
                        title: '⚠️ Análise de Riscos',
                        type: 'warning',
                        priority: 7,
                        required: false,
                        condition: (context)=>context.analysis.urgency === 'high' || context.analysis.complexity === 'advanced',
                        generator: (context)=>this.generateRiskAssessment(context)
                    },
                    {
                        id: 'best_practices',
                        title: '🎯 Melhores Práticas',
                        type: 'best-practices',
                        priority: 5,
                        required: false,
                        generator: (context)=>this.generateBestPractices(context)
                    },
                    {
                        id: 'next_steps',
                        title: '🚀 Próximos Passos',
                        type: 'next-steps',
                        priority: 9,
                        required: true,
                        generator: (context)=>this.generateNextSteps(context)
                    }
                ]
            },
            {
                id: 'client_communication',
                name: 'Comunicação com Cliente',
                description: 'Template para situações de comunicação e alinhamento com cliente',
                applicableWhen: (context)=>context.analysis.keywords.some((k)=>[
                            'cliente',
                            'comunicação',
                            'alinhamento',
                            'reunião'
                        ].includes(k)),
                priority: 8,
                sections: [
                    {
                        id: 'communication_analysis',
                        title: '📞 Análise da Situação',
                        type: 'analysis',
                        priority: 10,
                        required: true,
                        generator: (context)=>this.generateCommunicationAnalysis(context)
                    },
                    {
                        id: 'communication_strategy',
                        title: '💬 Estratégia de Comunicação',
                        type: 'recommendation',
                        priority: 9,
                        required: true,
                        generator: (context)=>this.generateCommunicationStrategy(context)
                    },
                    {
                        id: 'meeting_agenda',
                        title: '📋 Agenda de Reunião Sugerida',
                        type: 'detailed-guide',
                        priority: 7,
                        required: false,
                        condition: (context)=>context.analysis.keywords.includes('reunião'),
                        generator: (context)=>this.generateMeetingAgenda(context)
                    }
                ]
            }
        ]);
        // Planejamento Agent Templates
        this.templates.set('planejamento', [
            {
                id: 'strategic_planning',
                name: 'Planejamento Estratégico',
                description: 'Template completo para planejamento de campanhas',
                applicableWhen: (context)=>context.analysis.intent.includes('plan') || context.analysis.keywords.includes('planejamento') || context.analysis.keywords.includes('estratégia'),
                priority: 10,
                sections: [
                    {
                        id: 'strategic_overview',
                        title: '🎯 Visão Estratégica',
                        type: 'analysis',
                        priority: 10,
                        required: true,
                        generator: (context)=>this.generateStrategicOverview(context)
                    },
                    {
                        id: 'market_analysis',
                        title: '📊 Análise de Mercado',
                        type: 'analysis',
                        priority: 9,
                        required: true,
                        generator: (context)=>this.generateMarketAnalysis(context)
                    },
                    {
                        id: 'campaign_phases',
                        title: '📅 Fases da Campanha',
                        type: 'detailed-guide',
                        priority: 8,
                        required: true,
                        generator: (context)=>this.generateCampaignPhases(context)
                    },
                    {
                        id: 'budget_allocation',
                        title: '💰 Distribuição de Budget',
                        type: 'detailed-guide',
                        priority: 8,
                        required: true,
                        generator: (context)=>this.generateBudgetAllocation(context)
                    },
                    {
                        id: 'kpi_framework',
                        title: '📈 Framework de KPIs',
                        type: 'detailed-guide',
                        priority: 7,
                        required: true,
                        generator: (context)=>this.generateKPIFramework(context)
                    }
                ]
            }
        ]);
        // Mídia Agent Templates
        this.templates.set('midia', [
            {
                id: 'performance_analysis',
                name: 'Análise de Performance',
                description: 'Template para análise detalhada de performance de campanhas',
                applicableWhen: (context)=>context.analysis.intent.includes('analyze') || context.analysis.keywords.some((k)=>[
                            'performance',
                            'resultado',
                            'análise',
                            'métricas'
                        ].includes(k)),
                priority: 10,
                sections: [
                    {
                        id: 'performance_overview',
                        title: '📊 Visão Geral da Performance',
                        type: 'analysis',
                        priority: 10,
                        required: true,
                        generator: (context)=>this.generatePerformanceOverview(context)
                    },
                    {
                        id: 'detailed_metrics',
                        title: '📈 Métricas Detalhadas',
                        type: 'analysis',
                        priority: 9,
                        required: true,
                        generator: (context)=>this.generateDetailedMetrics(context)
                    },
                    {
                        id: 'optimization_opportunities',
                        title: '🎯 Oportunidades de Otimização',
                        type: 'recommendation',
                        priority: 8,
                        required: true,
                        generator: (context)=>this.generateOptimizationOpportunities(context)
                    },
                    {
                        id: 'competitive_insights',
                        title: '🏆 Insights Competitivos',
                        type: 'analysis',
                        priority: 7,
                        required: false,
                        generator: (context)=>this.generateCompetitiveInsights(context)
                    }
                ]
            }
        ]);
    }
    // Section generators implementation
    generateExecutiveSummary(context) {
        console.log('Generating executive summary...');
        const { analysis, input } = context;
        console.log('Analysis data:', {
            completeness: analysis.completeness,
            complexity: analysis.complexity,
            urgency: analysis.urgency,
            intent: analysis.intent
        });
        let content = `**Status:** Briefing analisado com ${Math.round(analysis.completeness * 100)}% de completude\n`;
        content += `**Complexidade:** ${this.translateComplexity(analysis.complexity)}\n`;
        content += `**Urgência:** ${this.translateUrgency(analysis.urgency)}\n`;
        content += `**Intenção Principal:** ${this.translateIntent(analysis.intent)}\n\n`;
        if (analysis.context.budget) {
            content += `**Budget Identificado:** R$ ${analysis.context.budget}\n`;
        }
        if (analysis.context.timeline) {
            content += `**Prazo:** ${analysis.context.timeline}\n`;
        }
        if (analysis.context.channels && analysis.context.channels.length > 0) {
            content += `**Canais Mencionados:** ${analysis.context.channels.join(', ')}\n`;
        }
        return {
            title: '📋 Resumo Executivo',
            content,
            type: 'analysis',
            priority: 10,
            metadata: {
                confidence: analysis.completeness
            }
        };
    }
    generateDetailedAnalysis(context) {
        const { analysis, input } = context;
        let content = '### Elementos Identificados\n\n';
        if (analysis.keywords.length > 0) {
            content += `**Palavras-chave principais:** ${analysis.keywords.slice(0, 5).join(', ')}\n\n`;
        }
        if (analysis.entities.length > 0) {
            content += `**Entidades detectadas:** ${analysis.entities.join(', ')}\n\n`;
        }
        content += '### Contexto do Projeto\n\n';
        if (Object.keys(analysis.context).length > 0) {
            for (const [key, value] of Object.entries(analysis.context)){
                content += `• **${this.formatContextKey(key)}:** ${value}\n`;
            }
        } else {
            content += '• Contexto adicional será coletado durante o processo de briefing\n';
        }
        content += '\n### Análise de Sentimento\n\n';
        content += `O tom geral da solicitação é **${this.translateSentiment(analysis.sentiment)}**, `;
        switch(analysis.sentiment){
            case 'positive':
                content += 'indicando confiança e expectativas positivas para o projeto.';
                break;
            case 'negative':
                content += 'sugerindo possíveis preocupações ou urgência que devem ser endereçadas.';
                break;
            default:
                content += 'mantendo um tom profissional e objetivo.';
        }
        return {
            title: '🔍 Análise Detalhada do Briefing',
            content,
            type: 'analysis',
            priority: 9
        };
    }
    generateMissingInformation(context) {
        const { analysis } = context;
        const missing = [];
        if (!analysis.context.budget) missing.push('Orçamento disponível');
        if (!analysis.context.timeline) missing.push('Cronograma desejado');
        if (!analysis.context.targetAge) missing.push('Público-alvo detalhado');
        if (!analysis.context.channels) missing.push('Canais preferenciais');
        let content = '### Informações Essenciais Faltantes\n\n';
        if (missing.length > 0) {
            missing.forEach((item)=>{
                content += `• ${item}\n`;
            });
            content += '\n### Impacto da Falta de Informações\n\n';
            content += '• **Planejamento:** Pode resultar em cronograma impreciso\n';
            content += '• **Orçamento:** Dificulta estimativa de custos e ROI\n';
            content += '• **Estratégia:** Limita precisão das recomendações\n';
            content += '• **Execução:** Pode causar retrabalho e atrasos\n';
            content += '\n### Recomendação\n\n';
            content += 'Agendar reunião de alinhamento para coletar informações faltantes antes de prosseguir com o planejamento detalhado.';
        } else {
            content += 'Todas as informações essenciais foram fornecidas. Excelente briefing!';
        }
        return {
            title: '❓ Informações Necessárias',
            content,
            type: 'warning',
            priority: 8
        };
    }
    generateStrategicRecommendations(context) {
        const { analysis, input } = context;
        const knowledge = this.knowledgeBase.getAgentKnowledge(context.agentId);
        const insights = this.knowledgeBase.getInsights(context.agentId, analysis);
        let content = '### Recomendações Baseadas na Análise\n\n';
        // Add insights from knowledge base
        insights.forEach((insight, index)=>{
            content += `${index + 1}. **${insight.priority.toUpperCase()}:** ${insight.insight}\n\n`;
        });
        // Add specific recommendations based on analysis
        if (analysis.urgency === 'high') {
            content += '• **Recursos Adicionais:** Considere alocar recursos extras para garantir qualidade\n';
            content += '• **Comunicação Intensiva:** Estabeleça check-ins diários com o cliente\n';
            content += '• **Cronograma Acelerado:** Defina marcos intermediários para controle\n\n';
        }
        if (analysis.complexity === 'advanced') {
            content += '• **Especialistas:** Envolver especialistas desde o início do projeto\n';
            content += '• **Faseamento:** Dividir projeto em fases menores para melhor controle\n';
            content += '• **Documentação:** Manter registro detalhado de todas as decisões\n\n';
        }
        // Budget-based recommendations
        if (analysis.context.budget) {
            const budgetValue = parseFloat(analysis.context.budget.replace(/[^\d]/g, ''));
            if (budgetValue < 10000) {
                content += '• **Foco Estratégico:** Com orçamento limitado, concentre em 1-2 canais principais\n';
                content += '• **ROI Máximo:** Priorize ações com maior potencial de retorno\n\n';
            } else if (budgetValue > 100000) {
                content += '• **Diversificação:** Budget permite estratégia multi-canal robusta\n';
                content += '• **Testes A/B:** Invista em testes para otimização contínua\n\n';
            }
        }
        // Channel-specific recommendations
        if (analysis.context.channels) {
            content += '• **Integração de Canais:** Garanta mensagem consistente entre ' + analysis.context.channels.join(', ') + '\n';
            content += '• **Attribution:** Configure tracking para medir performance de cada canal\n\n';
        }
        // Default recommendations if no specific insights
        if (insights.length === 0 && analysis.urgency === 'low' && analysis.complexity === 'basic') {
            content += '• **Planejamento Estruturado:** Defina objetivos claros e mensuráveis\n';
            content += '• **Cronograma Realista:** Estabeleça prazos factíveis para cada etapa\n';
            content += '• **Comunicação Regular:** Mantenha cliente informado sobre progresso\n';
            content += '• **Métricas de Sucesso:** Defina KPIs específicos para acompanhamento\n';
        }
        return {
            title: '💡 Recomendações Estratégicas',
            content,
            type: 'recommendation',
            priority: 8
        };
    }
    generateNextSteps(context) {
        const { analysis, input, agentId } = context;
        let content = '### Ações Imediatas (24-48h)\n\n';
        // Agent-specific immediate actions
        if (agentId === 'atendimento') {
            content += '1. ✅ Validar briefing com cliente\n';
            if (analysis.completeness < 0.7) {
                content += '2. 📋 Solicitar informações faltantes (ver seção de informações necessárias)\n';
            } else {
                content += '2. 📋 Confirmar detalhes do briefing\n';
            }
            content += '3. 👥 Definir equipe do projeto e responsabilidades\n';
            content += '4. 📅 Estabelecer cronograma de aprovações\n';
        } else if (agentId === 'planejamento') {
            content += '1. 📊 Analisar dados históricos e benchmarks\n';
            content += '2. 🎯 Definir personas detalhadas\n';
            content += '3. 📈 Estabelecer KPIs e métricas de sucesso\n';
            content += '4. 💰 Validar distribuição de budget\n';
        } else if (agentId === 'midia') {
            content += '1. 📊 Configurar tracking e analytics\n';
            content += '2. 🎯 Definir segmentações iniciais\n';
            content += '3. 💡 Criar primeiros criativos para teste\n';
            content += '4. 📈 Estabelecer baseline de performance\n';
        }
        if (analysis.urgency === 'high') {
            content += '5. 🚨 Ativar protocolo de urgência (recursos extras, comunicação diária)\n';
        }
        content += '\n### Próxima Semana\n\n';
        // Week 1 actions based on agent and context
        if (analysis.context.budget) {
            const budgetValue = parseFloat(analysis.context.budget.replace(/[^\d]/g, ''));
            if (budgetValue > 50000) {
                content += '• Desenvolver estratégia multi-canal detalhada\n';
                content += '• Planejar testes A/B para otimização\n';
            } else {
                content += '• Focar estratégia em canais de maior ROI\n';
                content += '• Otimizar budget para máximo impacto\n';
            }
        } else {
            content += '• Desenvolver estratégia baseada em objetivos\n';
        }
        content += '• Criar cronograma executivo detalhado\n';
        content += '• Definir marcos de aprovação e entrega\n';
        if (agentId === 'atendimento') {
            content += '• Preparar primeira apresentação para cliente\n';
            content += '• Estabelecer canais de comunicação\n';
        } else if (agentId === 'planejamento') {
            content += '• Finalizar estratégia e táticas\n';
            content += '• Preparar briefings para equipe criativa\n';
        } else if (agentId === 'midia') {
            content += '• Configurar campanhas iniciais\n';
            content += '• Preparar dashboards de monitoramento\n';
        }
        content += '\n### Próximas 2-4 Semanas\n\n';
        content += '• Implementar estratégia aprovada\n';
        content += '• Monitorar performance inicial\n';
        content += '• Ajustar táticas baseado em dados\n';
        content += '• Preparar relatórios de progresso\n';
        content += '\n### Acompanhamento\n\n';
        if (analysis.urgency === 'high') {
            content += '• **Check-ins:** Diários até estabilização\n';
            content += '• **Relatórios:** Status reports a cada 3 dias\n';
        } else {
            content += '• **Check-ins:** Semanais com cliente\n';
            content += '• **Relatórios:** Status reports quinzenais\n';
        }
        content += '• **Revisões:** Marcos de aprovação pré-definidos\n';
        content += '• **Otimização:** Ajustes baseados em performance\n';
        return {
            title: '🚀 Próximos Passos',
            content,
            type: 'next-steps',
            priority: 9
        };
    }
    generateTimelineBreakdown(context) {
        const { analysis, agentId } = context;
        let content = '### Cronograma Sugerido\n\n';
        // Determine timeline based on context
        let totalWeeks = 4 // default
        ;
        if (analysis.context.timeline) {
            const timelineText = analysis.context.timeline.toLowerCase();
            if (timelineText.includes('dia')) {
                const days = parseInt(timelineText.match(/\d+/)?.[0] || '30');
                totalWeeks = Math.ceil(days / 7);
            } else if (timelineText.includes('semana')) {
                totalWeeks = parseInt(timelineText.match(/\d+/)?.[0] || '4');
            } else if (timelineText.includes('mes')) {
                const months = parseInt(timelineText.match(/\d+/)?.[0] || '1');
                totalWeeks = months * 4;
            }
        }
        if (analysis.urgency === 'high') {
            totalWeeks = Math.max(2, Math.ceil(totalWeeks * 0.7)); // Reduce by 30% for urgent projects
        }
        // Generate phase-based timeline
        if (totalWeeks <= 2) {
            content += '**⚡ Cronograma Acelerado (2 semanas)**\n\n';
            content += '**Semana 1:**\n';
            content += '• Dias 1-2: Briefing e planejamento\n';
            content += '• Dias 3-5: Desenvolvimento e produção\n';
            content += '• Dias 6-7: Revisões e ajustes\n\n';
            content += '**Semana 2:**\n';
            content += '• Dias 8-10: Implementação\n';
            content += '• Dias 11-12: Testes e otimização\n';
            content += '• Dias 13-14: Lançamento e monitoramento\n';
        } else if (totalWeeks <= 4) {
            content += '**📅 Cronograma Padrão (4 semanas)**\n\n';
            content += '**Semana 1 - Planejamento:**\n';
            content += '• Validação de briefing e objetivos\n';
            content += '• Pesquisa de mercado e concorrência\n';
            content += '• Definição de estratégia e táticas\n';
            content += '• Aprovação de conceitos iniciais\n\n';
            content += '**Semana 2 - Desenvolvimento:**\n';
            content += '• Criação de materiais e conteúdo\n';
            content += '• Desenvolvimento de criativos\n';
            content += '• Setup de campanhas e tracking\n';
            content += '• Testes internos e ajustes\n\n';
            content += '**Semana 3 - Implementação:**\n';
            content += '• Lançamento das campanhas\n';
            content += '• Monitoramento inicial\n';
            content += '• Primeiros ajustes baseados em dados\n';
            content += '• Relatório de performance inicial\n\n';
            content += '**Semana 4 - Otimização:**\n';
            content += '• Análise de resultados\n';
            content += '• Otimizações baseadas em performance\n';
            content += '• Relatório final e recomendações\n';
            content += '• Planejamento de continuidade\n';
        } else {
            content += `**📈 Cronograma Estendido (${totalWeeks} semanas)**\n\n`;
            const phaseDuration = Math.ceil(totalWeeks / 4);
            content += `**Fase 1 - Planejamento (${phaseDuration} semanas):**\n`;
            content += '• Pesquisa aprofundada e análise de mercado\n';
            content += '• Desenvolvimento de estratégia detalhada\n';
            content += '• Criação de personas e jornada do cliente\n';
            content += '• Aprovação de conceitos e direcionamento\n\n';
            content += `**Fase 2 - Desenvolvimento (${phaseDuration} semanas):**\n`;
            content += '• Produção de materiais e conteúdo\n';
            content += '• Desenvolvimento de criativos variados\n';
            content += '• Setup completo de campanhas\n';
            content += '• Testes A/B e validações\n\n';
            content += `**Fase 3 - Lançamento (${phaseDuration} semanas):**\n`;
            content += '• Lançamento faseado das campanhas\n';
            content += '• Monitoramento intensivo\n';
            content += '• Otimizações contínuas\n';
            content += '• Expansão baseada em resultados\n\n';
            content += `**Fase 4 - Consolidação (${totalWeeks - phaseDuration * 3} semanas):**\n`;
            content += '• Análise completa de resultados\n';
            content += '• Documentação de aprendizados\n';
            content += '• Recomendações para continuidade\n';
            content += '• Planejamento de próximas fases\n';
        }
        content += '\n### Marcos Críticos\n\n';
        content += '🎯 **Aprovação de Estratégia:** Fim da primeira fase\n';
        content += '🎨 **Aprovação de Criativos:** Meio do desenvolvimento\n';
        content += '🚀 **Go-Live:** Início da implementação\n';
        content += '📊 **Primeira Análise:** 1 semana após lançamento\n';
        return {
            title: '📅 Cronograma Detalhado',
            content,
            type: 'detailed-guide',
            priority: 7
        };
    }
    generateApprovalProcess(context) {
        const { analysis, agentId } = context;
        let content = '### Fluxo de Aprovações\n\n';
        content += '**1. Aprovação de Estratégia**\n';
        content += '• Apresentação de briefing estruturado\n';
        content += '• Validação de objetivos e KPIs\n';
        content += '• Aprovação de cronograma e budget\n';
        content += '• **Prazo:** 2-3 dias úteis\n\n';
        content += '**2. Aprovação de Conceitos**\n';
        content += '• Apresentação de direcionamento criativo\n';
        content += '• Validação de tom de voz e messaging\n';
        content += '• Aprovação de abordagem estratégica\n';
        content += '• **Prazo:** 3-5 dias úteis\n\n';
        content += '**3. Aprovação de Materiais**\n';
        content += '• Revisão de peças criativas\n';
        content += '• Validação de textos e copy\n';
        content += '• Aprovação final para produção\n';
        content += '• **Prazo:** 2-3 dias úteis\n\n';
        if (analysis.urgency === 'high') {
            content += '### ⚡ Processo Acelerado\n\n';
            content += '• **Aprovações Simultâneas:** Conceito + materiais juntos\n';
            content += '• **Prazo Reduzido:** 24-48h por aprovação\n';
            content += '• **Comunicação Direta:** Calls ao invés de e-mails\n';
            content += '• **Aprovação Condicional:** Implementar com ajustes menores\n\n';
        }
        content += '### Responsabilidades\n\n';
        content += '**Cliente:**\n';
        content += '• Feedback em até 48h (ou 24h se urgente)\n';
        content += '• Consolidação de comentários internos\n';
        content += '• Decisões finais dentro do prazo\n\n';
        content += '**Agência:**\n';
        content += '• Apresentações claras e objetivas\n';
        content += '• Justificativas estratégicas\n';
        content += '• Implementação de ajustes solicitados\n';
        content += '• Comunicação proativa sobre prazos\n';
        return {
            title: '✅ Processo de Aprovação',
            content,
            type: 'detailed-guide',
            priority: 6
        };
    }
    generateRiskAssessment(context) {
        const { analysis } = context;
        let content = '### Riscos Identificados\n\n';
        // Add risks from analysis
        if (analysis.riskFactors && analysis.riskFactors.length > 0) {
            analysis.riskFactors.forEach((risk, index)=>{
                const severityEmoji = {
                    'low': '🟡',
                    'medium': '🟠',
                    'high': '🔴',
                    'critical': '🚨'
                }[risk.severity] || '⚠️';
                content += `**${severityEmoji} ${risk.type.toUpperCase()} - ${risk.severity.toUpperCase()}**\n`;
                content += `${risk.description}\n`;
                content += `**Probabilidade:** ${Math.round(risk.probability * 100)}% | **Impacto:** ${Math.round(risk.impact * 100)}%\n`;
                content += '**Mitigação:**\n';
                risk.mitigation.forEach((action)=>{
                    content += `• ${action}\n`;
                });
                content += '\n';
            });
        } else {
            // Generate common risks based on analysis
            if (analysis.urgency === 'high') {
                content += '**🔴 CRONOGRAMA - ALTO**\n';
                content += 'Prazo apertado pode comprometer qualidade ou gerar estresse na equipe\n';
                content += '**Mitigação:**\n';
                content += '• Alocar recursos adicionais\n';
                content += '• Simplificar escopo se necessário\n';
                content += '• Comunicação intensiva com cliente\n\n';
            }
            if (analysis.completeness < 0.6) {
                content += '**🟠 BRIEFING - MÉDIO**\n';
                content += 'Informações incompletas podem gerar retrabalho e atrasos\n';
                content += '**Mitigação:**\n';
                content += '• Reunião de alinhamento urgente\n';
                content += '• Checklist de informações obrigatórias\n';
                content += '• Validação constante com cliente\n\n';
            }
            if (analysis.complexity === 'advanced') {
                content += '**🟠 COMPLEXIDADE - MÉDIO**\n';
                content += 'Projeto complexo requer expertise específica e coordenação cuidadosa\n';
                content += '**Mitigação:**\n';
                content += '• Envolver especialistas desde o início\n';
                content += '• Dividir em fases menores\n';
                content += '• Documentação detalhada de processos\n\n';
            }
        }
        content += '### Monitoramento de Riscos\n\n';
        content += '• **Revisões Semanais:** Avaliação de status dos riscos\n';
        content += '• **Alertas Antecipados:** Comunicação imediata se risco se materializar\n';
        content += '• **Planos de Contingência:** Ações alternativas preparadas\n';
        content += '• **Escalação:** Processo claro para decisões críticas\n';
        return {
            title: '⚠️ Análise de Riscos',
            content,
            type: 'warning',
            priority: 7
        };
    }
    generateBestPractices(context) {
        const { analysis, agentId } = context;
        const knowledge = this.knowledgeBase.getAgentKnowledge(agentId);
        let content = '### Práticas Recomendadas\n\n';
        // Add best practices from knowledge base
        if (knowledge && knowledge.bestPractices.length > 0) {
            const relevantPractices = knowledge.bestPractices.slice(0, 2) // Top 2 most relevant
            ;
            relevantPractices.forEach((practice)=>{
                content += `**${practice.title}**\n`;
                content += `${practice.description}\n\n`;
                content += '**Implementação:**\n';
                practice.implementation.forEach((step)=>{
                    content += `• ${step}\n`;
                });
                content += '\n**Benefícios:**\n';
                practice.benefits.forEach((benefit)=>{
                    content += `• ${benefit}\n`;
                });
                content += '\n**Evitar:**\n';
                practice.commonMistakes.forEach((mistake)=>{
                    content += `• ${mistake}\n`;
                });
                content += '\n';
            });
        }
        // Add general best practices based on context
        if (analysis.context.budget) {
            content += '**Gestão de Budget**\n';
            content += '• Monitore gastos semanalmente\n';
            content += '• Reserve 10-15% para otimizações\n';
            content += '• Documente todas as decisões de investimento\n';
            content += '• Meça ROI de cada canal separadamente\n\n';
        }
        if (analysis.context.channels && analysis.context.channels.length > 1) {
            content += '**Integração Multi-canal**\n';
            content += '• Mantenha mensagem consistente entre canais\n';
            content += '• Configure attribution modeling adequado\n';
            content += '• Teste criativos específicos para cada canal\n';
            content += '• Monitore performance comparativa\n\n';
        }
        content += '**Comunicação com Cliente**\n';
        content += '• Updates regulares mesmo sem novidades\n';
        content += '• Dados sempre acompanhados de insights\n';
        content += '• Transparência total sobre desafios\n';
        content += '• Recomendações proativas de melhorias\n';
        return {
            title: '🎯 Melhores Práticas',
            content,
            type: 'best-practices',
            priority: 5
        };
    }
    generateCommunicationAnalysis(context) {
        return {
            title: '📞 Análise da Situação',
            content: 'Análise da comunicação...',
            type: 'analysis',
            priority: 10
        };
    }
    generateCommunicationStrategy(context) {
        return {
            title: '💬 Estratégia de Comunicação',
            content: 'Estratégia de comunicação...',
            type: 'recommendation',
            priority: 9
        };
    }
    generateMeetingAgenda(context) {
        return {
            title: '📋 Agenda de Reunião Sugerida',
            content: 'Agenda de reunião...',
            type: 'detailed-guide',
            priority: 7
        };
    }
    generateStrategicOverview(context) {
        return {
            title: '🎯 Visão Estratégica',
            content: 'Visão estratégica...',
            type: 'analysis',
            priority: 10
        };
    }
    generateMarketAnalysis(context) {
        return {
            title: '📊 Análise de Mercado',
            content: 'Análise de mercado...',
            type: 'analysis',
            priority: 9
        };
    }
    generateCampaignPhases(context) {
        return {
            title: '📅 Fases da Campanha',
            content: 'Fases da campanha...',
            type: 'detailed-guide',
            priority: 8
        };
    }
    generateBudgetAllocation(context) {
        return {
            title: '💰 Distribuição de Budget',
            content: 'Distribuição de budget...',
            type: 'detailed-guide',
            priority: 8
        };
    }
    generateKPIFramework(context) {
        return {
            title: '📈 Framework de KPIs',
            content: 'Framework de KPIs...',
            type: 'detailed-guide',
            priority: 7
        };
    }
    generatePerformanceOverview(context) {
        return {
            title: '📊 Visão Geral da Performance',
            content: 'Visão geral da performance...',
            type: 'analysis',
            priority: 10
        };
    }
    generateDetailedMetrics(context) {
        return {
            title: '📈 Métricas Detalhadas',
            content: 'Métricas detalhadas...',
            type: 'analysis',
            priority: 9
        };
    }
    generateOptimizationOpportunities(context) {
        return {
            title: '🎯 Oportunidades de Otimização',
            content: 'Oportunidades de otimização...',
            type: 'recommendation',
            priority: 8
        };
    }
    generateCompetitiveInsights(context) {
        return {
            title: '🏆 Insights Competitivos',
            content: 'Insights competitivos...',
            type: 'analysis',
            priority: 7
        };
    }
    generateFallbackResponse(context) {
        console.log('Generating fallback response for agent:', context.agentId);
        // Generate basic sections using the existing generators
        const sections = [];
        try {
            sections.push(this.generateExecutiveSummary(context));
        } catch (error) {
            console.error('Error generating executive summary in fallback:', error);
        }
        try {
            sections.push(this.generateStrategicRecommendations(context));
        } catch (error) {
            console.error('Error generating recommendations in fallback:', error);
        }
        try {
            sections.push(this.generateNextSteps(context));
        } catch (error) {
            console.error('Error generating next steps in fallback:', error);
        }
        // If all generators failed, return a basic response
        if (sections.length === 0) {
            sections.push({
                title: '📋 Análise Básica',
                content: `Análise do input recebido para o agente ${context.agentId}.\n\nInput analisado: "${context.input}"\n\nRecomendamos fornecer mais detalhes para uma análise mais completa.`,
                type: 'analysis',
                priority: 5
            });
        }
        return sections;
    }
    // Utility methods
    translateComplexity(complexity) {
        const translations = {
            'basic': 'Básica',
            'intermediate': 'Intermediária',
            'advanced': 'Avançada'
        };
        return translations[complexity] || complexity;
    }
    translateUrgency(urgency) {
        const translations = {
            'low': 'Baixa',
            'medium': 'Média',
            'high': 'Alta'
        };
        return translations[urgency] || urgency;
    }
    translateIntent(intent) {
        const translations = {
            'create_briefing': 'Criação de Briefing',
            'analyze_performance': 'Análise de Performance',
            'plan_campaign': 'Planejamento de Campanha',
            'optimize': 'Otimização',
            'report': 'Relatório',
            'troubleshoot': 'Resolução de Problemas',
            'general_inquiry': 'Consulta Geral'
        };
        return translations[intent] || intent;
    }
    translateSentiment(sentiment) {
        const translations = {
            'positive': 'positivo',
            'neutral': 'neutro',
            'negative': 'negativo'
        };
        return translations[sentiment] || sentiment;
    }
    formatContextKey(key) {
        const translations = {
            'budget': 'Orçamento',
            'timeline': 'Cronograma',
            'targetAge': 'Faixa Etária',
            'channels': 'Canais'
        };
        return translations[key] || key;
    }
}
}),
"[project]/src/lib/agents/enhancedAgentSystem.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Enhanced Agent System
 *
 * This module integrates all components of the enhanced response system to provide
 * comprehensive, intelligent, and contextually relevant agent responses.
 */ __turbopack_context__.s([
    "EnhancedAgentSystem",
    ()=>EnhancedAgentSystem,
    "analyzeUserInput",
    ()=>analyzeUserInput,
    "enhancedAgentSystem",
    ()=>enhancedAgentSystem,
    "generateAgentResponse",
    ()=>generateAgentResponse
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$responseEngine$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents/responseEngine.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$intelligenceLayer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents/intelligenceLayer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$responseTemplates$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents/responseTemplates.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$knowledgeBase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents/knowledgeBase.ts [app-ssr] (ecmascript)");
;
;
;
;
class EnhancedAgentSystem {
    static instance;
    responseEngine;
    intelligenceLayer;
    templateEngine;
    knowledgeBase;
    agentConfigs = new Map();
    constructor(){
        this.responseEngine = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$responseEngine$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ResponseEngine"].getInstance();
        this.intelligenceLayer = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$intelligenceLayer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IntelligenceLayer"].getInstance();
        this.templateEngine = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$responseTemplates$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ResponseTemplateEngine"].getInstance();
        this.knowledgeBase = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$knowledgeBase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AgentKnowledgeBase"].getInstance();
        this.initializeAgentConfigs();
    }
    static getInstance() {
        if (!EnhancedAgentSystem.instance) {
            EnhancedAgentSystem.instance = new EnhancedAgentSystem();
        }
        return EnhancedAgentSystem.instance;
    }
    /**
   * Main method to generate enhanced agent responses
   */ async generateResponse(agentId, input, userContext) {
        const startTime = Date.now();
        const config = this.agentConfigs.get(agentId);
        if (!config) {
            throw new Error(`Agent configuration not found for: ${agentId}`);
        }
        try {
            // Step 1: Perform advanced input analysis
            const analysis = config.enableAdvancedAnalysis ? this.intelligenceLayer.performAdvancedAnalysis(input) : this.performBasicAnalysis(input);
            // Step 2: Generate template-based response
            const templateContext = {
                agentId,
                analysis,
                input,
                userContext
            };
            const templateSections = this.templateEngine.generateResponse(templateContext);
            // Step 3: Generate enhanced response using response engine
            const enhancedResponse = this.responseEngine.generateResponse(agentId, input, analysis);
            // Step 4: Merge template sections with enhanced response
            const mergedSections = this.mergeSections(templateSections, enhancedResponse.sections);
            // Step 5: Apply agent-specific enhancements
            const finalResponse = {
                ...enhancedResponse,
                sections: mergedSections,
                confidence: this.calculateFinalConfidence(analysis, mergedSections),
                completeness: this.calculateFinalCompleteness(analysis, mergedSections)
            };
            // Step 6: Format output based on agent style
            const formattedOutput = this.formatResponse(finalResponse, config.responseStyle);
            // Step 7: Calculate metadata
            const processingTime = Date.now() - startTime;
            const metadata = this.calculateMetadata(analysis, finalResponse, processingTime, config);
            // Step 8: Quality validation
            if (metadata.qualityScore < config.minQualityThreshold) {
                console.warn(`Response quality below threshold for agent ${agentId}: ${metadata.qualityScore}`);
            // Could trigger fallback or enhancement here
            }
            return {
                id: this.generateResponseId(),
                agentId,
                input,
                analysis,
                response: finalResponse,
                formattedOutput,
                metadata,
                timestamp: new Date()
            };
        } catch (error) {
            console.error(`Error generating response for agent ${agentId}:`, error);
            throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Batch processing for multiple inputs
   */ async generateBatchResponses(agentId, inputs, userContext) {
        const responses = [];
        for (const input of inputs){
            try {
                const response = await this.generateResponse(agentId, input, userContext);
                responses.push(response);
            } catch (error) {
                console.error(`Failed to process input: ${input}`, error);
            // Continue with other inputs
            }
        }
        return responses;
    }
    /**
   * Get agent capabilities and specializations
   */ getAgentCapabilities(agentId) {
        const config = this.agentConfigs.get(agentId);
        const knowledge = this.knowledgeBase.getAgentKnowledge(agentId);
        if (!config || !knowledge) return null;
        return {
            specializations: config.specialization,
            knowledgeAreas: knowledge.specializations,
            responseStyle: config.responseStyle,
            qualityThreshold: config.minQualityThreshold
        };
    }
    /**
   * Analyze input without generating full response (for preview/validation)
   */ analyzeInput(input) {
        return this.intelligenceLayer.performAdvancedAnalysis(input);
    }
    initializeAgentConfigs() {
        this.agentConfigs.set('atendimento', {
            id: 'atendimento',
            name: 'Agente de Atendimento',
            specialization: [
                'briefing',
                'client-communication',
                'project-management'
            ],
            responseStyle: 'comprehensive',
            minQualityThreshold: 0.8,
            enableAdvancedAnalysis: true
        });
        this.agentConfigs.set('planejamento', {
            id: 'planejamento',
            name: 'Agente de Planejamento',
            specialization: [
                'strategy',
                'planning',
                'analysis'
            ],
            responseStyle: 'detailed',
            minQualityThreshold: 0.85,
            enableAdvancedAnalysis: true
        });
        this.agentConfigs.set('midia', {
            id: 'midia',
            name: 'Agente de Mídia',
            specialization: [
                'performance',
                'optimization',
                'analytics'
            ],
            responseStyle: 'detailed',
            minQualityThreshold: 0.8,
            enableAdvancedAnalysis: true
        });
    }
    performBasicAnalysis(input) {
        // Fallback to basic analysis if advanced is disabled
        const basicAnalysis = this.responseEngine.analyzeInput(input);
        // Convert to AdvancedAnalysis format with minimal additional data
        return {
            ...basicAnalysis,
            namedEntities: [],
            topicClusters: [],
            linguisticFeatures: {
                readabilityScore: 50,
                formalityLevel: 'neutral',
                technicalComplexity: 0.1,
                emotionalIntensity: 0.1,
                questionCount: (input.match(/\?/g) || []).length,
                imperativeCount: 0,
                averageSentenceLength: input.split(/[.!?]+/).length > 0 ? input.split(/\s+/).length / input.split(/[.!?]+/).length : 0
            },
            businessContext: {
                industry: [],
                businessStage: 'growth',
                marketingMaturity: 'intermediate',
                budgetRange: 'small',
                timeframe: 'medium-term'
            },
            riskFactors: [],
            opportunityIndicators: []
        };
    }
    mergeSections(templateSections, enhancedSections) {
        console.log('Template sections:', templateSections.length);
        console.log('Enhanced sections:', enhancedSections.length);
        // If we have template sections, use them as primary source
        if (templateSections.length > 0) {
            console.log('Using template sections as primary');
            return templateSections.sort((a, b)=>(b.priority || 0) - (a.priority || 0));
        }
        // Fallback to enhanced sections if no templates
        console.log('Falling back to enhanced sections');
        return enhancedSections.sort((a, b)=>(b.priority || 0) - (a.priority || 0));
    }
    calculateFinalConfidence(analysis, sections) {
        let confidence = analysis.completeness * 0.4 // Base on input completeness
        ;
        // Boost confidence based on number of quality sections
        confidence += Math.min(sections.length * 0.1, 0.4);
        // Adjust based on risk factors
        if (analysis.riskFactors.length > 0) {
            const avgRiskSeverity = analysis.riskFactors.reduce((sum, risk)=>{
                const severityScore = {
                    low: 0.1,
                    medium: 0.3,
                    high: 0.5,
                    critical: 0.7
                };
                return sum + (severityScore[risk.severity] || 0.3);
            }, 0) / analysis.riskFactors.length;
            confidence -= avgRiskSeverity * 0.2;
        }
        return Math.max(0.1, Math.min(1.0, confidence));
    }
    calculateFinalCompleteness(analysis, sections) {
        let completeness = analysis.completeness * 0.5 // Base on input completeness
        ;
        // Add completeness based on section coverage
        const requiredSectionTypes = [
            'analysis',
            'recommendation',
            'next-steps'
        ];
        const presentTypes = new Set(sections.map((s)=>s.type));
        const typesCovered = requiredSectionTypes.filter((type)=>presentTypes.has(type)).length;
        completeness += typesCovered / requiredSectionTypes.length * 0.5;
        return Math.min(1.0, completeness);
    }
    formatResponse(response, style) {
        let formatted = `# ${response.summary}\n\n`;
        // Filter sections based on style
        let sectionsToInclude = response.sections;
        if (style === 'concise') {
            sectionsToInclude = response.sections.filter((s)=>s.type === 'analysis' || s.type === 'recommendation' || s.type === 'next-steps').slice(0, 3);
        } else if (style === 'detailed') {
            sectionsToInclude = response.sections.slice(0, 6);
        }
        // comprehensive includes all sections
        // Format sections
        sectionsToInclude.forEach((section)=>{
            formatted += `## ${section.title}\n\n${section.content}\n\n`;
        });
        // Add follow-up questions for detailed and comprehensive styles
        if (style !== 'concise' && response.followUpQuestions.length > 0) {
            formatted += `## 🤔 Perguntas para Aprofundamento\n\n`;
            response.followUpQuestions.forEach((question, index)=>{
                formatted += `${index + 1}. ${question}\n`;
            });
            formatted += '\n';
        }
        // Add action items
        if (response.actionItems.length > 0) {
            formatted += `## ✅ Itens de Ação\n\n`;
            response.actionItems.forEach((item, index)=>{
                formatted += `- [ ] ${item}\n`;
            });
            formatted += '\n';
        }
        // Add metadata footer for comprehensive style
        if (style === 'comprehensive') {
            formatted += `---\n\n`;
            formatted += `**Tempo de leitura estimado:** ${response.estimatedReadTime} min | `;
            formatted += `**Confiança:** ${Math.round(response.confidence * 100)}% | `;
            formatted += `**Completude:** ${Math.round(response.completeness * 100)}%\n`;
        }
        return formatted;
    }
    calculateMetadata(analysis, response, processingTime, config) {
        // Calculate quality score based on multiple factors
        let qualityScore = 0;
        // Content quality (40%)
        qualityScore += response.completeness * 0.4;
        // Analysis depth (30%)
        const analysisDepth = (analysis.namedEntities.length > 0 ? 0.1 : 0) + (analysis.topicClusters.length > 0 ? 0.1 : 0) + (analysis.riskFactors.length > 0 ? 0.05 : 0) + (analysis.opportunityIndicators.length > 0 ? 0.05 : 0);
        qualityScore += Math.min(analysisDepth, 0.3);
        // Response structure (20%)
        const structureScore = Math.min(response.sections.length * 0.03, 0.2);
        qualityScore += structureScore;
        // Actionability (10%)
        const actionabilityScore = Math.min(response.actionItems.length * 0.02, 0.1);
        qualityScore += actionabilityScore;
        return {
            processingTime,
            confidence: response.confidence,
            completeness: response.completeness,
            qualityScore: Math.min(1.0, qualityScore),
            templateUsed: 'dynamic',
            riskFactors: analysis.riskFactors.length,
            opportunities: analysis.opportunityIndicators.length,
            followUpRecommended: response.followUpQuestions.length > 0 || analysis.completeness < 0.7
        };
    }
    generateResponseId() {
        return `resp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
const enhancedAgentSystem = EnhancedAgentSystem.getInstance();
async function generateAgentResponse(agentId, input, userContext) {
    return enhancedAgentSystem.generateResponse(agentId, input, userContext);
}
function analyzeUserInput(input) {
    return enhancedAgentSystem.analyzeInput(input);
}
}),
"[project]/src/app/dashboard/agentes/page.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>AgentesPage
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/ProtectedRoute.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/DashboardLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$UserGroupIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserGroupIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js [app-ssr] (ecmascript) <export default as UserGroupIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$CalendarDaysIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarDaysIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js [app-ssr] (ecmascript) <export default as CalendarDaysIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChartBarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChartBarIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js [app-ssr] (ecmascript) <export default as ChartBarIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$SparklesIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SparklesIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js [app-ssr] (ecmascript) <export default as SparklesIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$DocumentTextIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DocumentTextIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js [app-ssr] (ecmascript) <export default as DocumentTextIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ArrowRightIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRightIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js [app-ssr] (ecmascript) <export default as ArrowRightIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClipboardDocumentIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ClipboardDocumentIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentIcon.js [app-ssr] (ecmascript) <export default as ClipboardDocumentIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$enhancedAgentSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents/enhancedAgentSystem.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const agents = [
    {
        id: 'atendimento',
        name: 'Agente de Atendimento',
        description: 'Organiza briefings, acompanha prazos e mantém a comunicação com clientes sempre em dia.',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$UserGroupIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserGroupIcon$3e$__["UserGroupIcon"],
        color: 'blue',
        capabilities: [
            'Estrutura briefings automaticamente',
            'Cria cronogramas de aprovação',
            'Monitora prazos e entregas',
            'Gera relatórios de status'
        ],
        inputPlaceholder: 'Cole aqui o briefing do cliente ou descreva a demanda...',
        examples: [
            'Briefing: Campanha para lançamento de produto X, público jovem 18-25 anos, budget R$ 50k',
            'Cliente solicitou alterações no cronograma da campanha Y',
            'Preciso organizar as aprovações para a campanha de Black Friday'
        ]
    },
    {
        id: 'planejamento',
        name: 'Agente de Planejamento',
        description: 'Gera cronogramas inteligentes, insights estratégicos e organiza toda a execução da campanha.',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$CalendarDaysIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarDaysIcon$3e$__["CalendarDaysIcon"],
        color: 'green',
        capabilities: [
            'Cronogramas automáticos',
            'Análise de concorrência',
            'Sugestões de estratégia',
            'Distribuição de budget'
        ],
        inputPlaceholder: 'Descreva o projeto ou campanha que precisa ser planejada...',
        examples: [
            'Campanha de 60 dias para lançamento de app, budget R$ 100k, 3 fases',
            'Planejamento estratégico para Black Friday, múltiplos canais',
            'Cronograma para campanha institucional com prazo apertado'
        ]
    },
    {
        id: 'midia',
        name: 'Agente de Mídia',
        description: 'Analisa performance, sugere otimizações e gera relatórios com insights acionáveis.',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChartBarIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChartBarIcon$3e$__["ChartBarIcon"],
        color: 'purple',
        capabilities: [
            'Análise de KPIs',
            'Sugestões de otimização',
            'Relatórios automatizados',
            'Alertas de performance'
        ],
        inputPlaceholder: 'Cole dados de performance ou descreva a campanha para análise...',
        examples: [
            'CTR: 2.1%, CPC: R$ 0.85, Budget gasto: R$ 15k de R$ 30k',
            'Campanha Instagram com baixo engajamento, preciso otimizar',
            'Análise de performance da campanha de verão nos últimos 30 dias'
        ]
    }
];
function AgentesPage() {
    const [selectedAgent, setSelectedAgent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [input, setInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [isProcessing, setIsProcessing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [result, setResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [agentResponse, setAgentResponse] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [inputAnalysis, setInputAnalysis] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showAnalysis, setShowAnalysis] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [copySuccess, setCopySuccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const currentAgent = agents.find((agent)=>agent.id === selectedAgent);
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!input.trim() || !currentAgent) return;
        setIsProcessing(true);
        setResult(null);
        setAgentResponse(null);
        try {
            // Analyze input first for preview
            const analysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$enhancedAgentSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["analyzeUserInput"])(input);
            setInputAnalysis(analysis);
            // Generate enhanced response
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$enhancedAgentSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateAgentResponse"])(currentAgent.id, input);
            setAgentResponse(response);
            setResult(response.formattedOutput);
        } catch (error) {
            console.error('Error generating response:', error);
            setResult('Erro ao processar solicitação. Tente novamente.');
        } finally{
            setIsProcessing(false);
        }
    };
    const handleAnalyzeInput = ()=>{
        if (!input.trim()) return;
        const analysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2f$enhancedAgentSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["analyzeUserInput"])(input);
        setInputAnalysis(analysis);
        setShowAnalysis(true);
    };
    const handleCopyResponse = async ()=>{
        if (!result) return;
        try {
            await navigator.clipboard.writeText(result);
            setCopySuccess(true);
            setTimeout(()=>setCopySuccess(false), 2000);
        } catch (error) {
            console.error('Erro ao copiar:', error);
        }
    };
    const resetAgent = ()=>{
        setSelectedAgent(null);
        setInput('');
        setResult(null);
        setAgentResponse(null);
        setInputAnalysis(null);
        setShowAnalysis(false);
        setCopySuccess(false);
    };
    if (!selectedAgent) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold text-gray-900",
                                    children: "Agentes Inteligentes"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                    lineNumber: 151,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-600",
                                    children: "Escolha um agente especializado para ajudar com sua tarefa"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                    lineNumber: 152,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                            lineNumber: 150,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
                            children: agents.map((agent)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer",
                                    onClick: ()=>setSelectedAgent(agent.id),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-3 mb-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: agent.id === 'atendimento' ? 'bg-blue-100 p-3 rounded-lg' : agent.id === 'planejamento' ? 'bg-green-100 p-3 rounded-lg' : 'bg-purple-100 p-3 rounded-lg',
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(agent.icon, {
                                                            className: agent.id === 'atendimento' ? 'h-6 w-6 text-blue-600' : agent.id === 'planejamento' ? 'h-6 w-6 text-green-600' : 'h-6 w-6 text-purple-600'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                            lineNumber: 165,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 160,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "font-semibold text-gray-900",
                                                        children: agent.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 171,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 159,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-600 mb-4",
                                                children: agent.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 174,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-2 mb-6",
                                                children: agent.capabilities.map((capability, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-1.5 h-1.5 bg-primary-500 rounded-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 179,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm text-gray-700",
                                                                children: capability
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 180,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, idx, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 178,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 176,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",
                                                children: [
                                                    "🚀 Usar Agente",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ArrowRightIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRightIcon$3e$__["ArrowRightIcon"], {
                                                        className: "h-5 w-5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 187,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 185,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 158,
                                        columnNumber: 19
                                    }, this)
                                }, agent.id, false, {
                                    fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                    lineNumber: 157,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                            lineNumber: 155,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                    lineNumber: 149,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                lineNumber: 148,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
            lineNumber: 147,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: resetAgent,
                                className: "text-gray-500 hover:text-gray-700",
                                children: "← Voltar"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                lineNumber: 205,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: currentAgent?.id === 'atendimento' ? 'bg-blue-100 p-2 rounded-lg' : currentAgent?.id === 'planejamento' ? 'bg-green-100 p-2 rounded-lg' : 'bg-purple-100 p-2 rounded-lg',
                                        children: currentAgent?.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(currentAgent.icon, {
                                            className: currentAgent?.id === 'atendimento' ? 'h-5 w-5 text-blue-600' : currentAgent?.id === 'planejamento' ? 'h-5 w-5 text-green-600' : 'h-5 w-5 text-purple-600'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                            lineNumber: 217,
                                            columnNumber: 40
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 212,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-xl font-bold text-gray-900",
                                                children: currentAgent?.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 224,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600",
                                                children: currentAgent?.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 225,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 223,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                lineNumber: 211,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                        lineNumber: 204,
                        columnNumber: 11
                    }, this),
                    !result ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow p-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                onSubmit: handleSubmit,
                                className: "space-y-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "input",
                                                className: "block text-sm font-medium text-gray-700 mb-2",
                                                children: "Descreva sua demanda"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 234,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                id: "input",
                                                value: input,
                                                onChange: (e)=>setInput(e.target.value),
                                                rows: 6,
                                                className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none",
                                                placeholder: currentAgent?.inputPlaceholder,
                                                required: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 237,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 233,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: handleAnalyzeInput,
                                                disabled: !input.trim(),
                                                className: "flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2",
                                                children: "🔍 Analisar Input"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 249,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "submit",
                                                disabled: isProcessing || !input.trim(),
                                                className: "flex-2 bg-blue-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2 border-2 border-blue-600",
                                                style: {
                                                    minWidth: '200px'
                                                },
                                                children: isProcessing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "animate-spin rounded-full h-5 w-5 border-b-2 border-white"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                            lineNumber: 266,
                                                            columnNumber: 25
                                                        }, this),
                                                        "⏳ Processando..."
                                                    ]
                                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$SparklesIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__SparklesIcon$3e$__["SparklesIcon"], {
                                                            className: "h-5 w-5"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                            lineNumber: 271,
                                                            columnNumber: 25
                                                        }, this),
                                                        "🚀 Processar"
                                                    ]
                                                }, void 0, true)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 258,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 248,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                lineNumber: 232,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-8 pt-6 border-t border-gray-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-sm font-medium text-gray-900 mb-3",
                                        children: "💡 Exemplos:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 281,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-2",
                                        children: currentAgent?.examples.map((example, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>setInput(example),
                                                className: "text-left text-sm text-blue-600 hover:text-blue-700 block w-full p-2 rounded hover:bg-blue-50 transition-colors",
                                                children: [
                                                    "💡 ",
                                                    example
                                                ]
                                            }, idx, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 284,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 282,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                lineNumber: 280,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                        lineNumber: 231,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            inputAnalysis && showAnalysis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-blue-50 rounded-lg border border-blue-200 p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between mb-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-sm font-semibold text-blue-900",
                                                children: "Análise do Input"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 301,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>setShowAnalysis(false),
                                                className: "text-blue-600 hover:text-blue-800 text-sm",
                                                children: "Ocultar"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 302,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 300,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-blue-700 font-medium",
                                                        children: "Complexidade:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 311,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "ml-1 text-blue-900 capitalize",
                                                        children: inputAnalysis.complexity
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 312,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 310,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-blue-700 font-medium",
                                                        children: "Urgência:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 315,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "ml-1 text-blue-900 capitalize",
                                                        children: inputAnalysis.urgency
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 316,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 314,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-blue-700 font-medium",
                                                        children: "Completude:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 319,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "ml-1 text-blue-900",
                                                        children: [
                                                            Math.round(inputAnalysis.completeness * 100),
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 320,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 318,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-blue-700 font-medium",
                                                        children: "Sentimento:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 323,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "ml-1 text-blue-900 capitalize",
                                                        children: inputAnalysis.sentiment
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 324,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 322,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 309,
                                        columnNumber: 19
                                    }, this),
                                    inputAnalysis.riskFactors.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-3 pt-3 border-t border-blue-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-blue-700 font-medium text-sm",
                                                children: "Riscos Identificados:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 329,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "ml-1 text-red-600 text-sm",
                                                children: [
                                                    inputAnalysis.riskFactors.length,
                                                    " fatores"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 330,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 328,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                lineNumber: 299,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow",
                                children: [
                                    agentResponse && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "border-b border-gray-200 p-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-semibold text-gray-900",
                                                        children: "Métricas de Qualidade"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 341,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-4 text-xs text-gray-600",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: [
                                                                    "Processado em ",
                                                                    agentResponse.metadata.processingTime,
                                                                    "ms"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 343,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: [
                                                                    "Tempo de leitura: ",
                                                                    agentResponse.response.estimatedReadTime,
                                                                    " min"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 344,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 342,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 340,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-3 gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-lg font-bold text-green-600",
                                                                children: [
                                                                    Math.round(agentResponse.metadata.confidence * 100),
                                                                    "%"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 349,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-xs text-gray-600",
                                                                children: "Confiança"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 352,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 348,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-lg font-bold text-blue-600",
                                                                children: [
                                                                    Math.round(agentResponse.metadata.completeness * 100),
                                                                    "%"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 355,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-xs text-gray-600",
                                                                children: "Completude"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 358,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 354,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-lg font-bold text-purple-600",
                                                                children: [
                                                                    Math.round(agentResponse.metadata.qualityScore * 100),
                                                                    "%"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 361,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-xs text-gray-600",
                                                                children: "Qualidade"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 364,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 360,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 347,
                                                columnNumber: 21
                                            }, this),
                                            agentResponse.metadata.followUpRecommended && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800",
                                                children: "💡 Recomendamos uma conversa de acompanhamento para esclarecer detalhes adicionais."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 368,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 339,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$DocumentTextIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DocumentTextIcon$3e$__["DocumentTextIcon"], {
                                                                className: "h-5 w-5 text-green-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 378,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                className: "text-lg font-semibold text-gray-900",
                                                                children: "Resultado Processado"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 379,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 377,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: handleCopyResponse,
                                                        className: "flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ClipboardDocumentIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ClipboardDocumentIcon$3e$__["ClipboardDocumentIcon"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 385,
                                                                columnNumber: 23
                                                            }, this),
                                                            copySuccess ? 'Copiado!' : 'Copiar Resposta'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 381,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 376,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "prose max-w-none",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                                    className: "whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed",
                                                    children: result
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                    lineNumber: 391,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 390,
                                                columnNumber: 19
                                            }, this),
                                            agentResponse && agentResponse.response.actionItems.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-6 p-4 bg-gray-50 rounded-lg",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-semibold text-gray-900 mb-3",
                                                        children: "Itens de Ação Identificados"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 399,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "space-y-2",
                                                        children: agentResponse.response.actionItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                className: "flex items-start gap-2 text-sm text-gray-700",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-blue-500 mt-1",
                                                                        children: "•"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                        lineNumber: 403,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    item
                                                                ]
                                                            }, index, true, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 402,
                                                                columnNumber: 27
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 400,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 398,
                                                columnNumber: 21
                                            }, this),
                                            agentResponse && agentResponse.response.followUpQuestions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-4 p-4 bg-blue-50 rounded-lg",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-sm font-semibold text-blue-900 mb-3",
                                                        children: "Perguntas para Aprofundamento"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 414,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "space-y-2",
                                                        children: agentResponse.response.followUpQuestions.map((question, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                className: "text-sm text-blue-800",
                                                                children: [
                                                                    index + 1,
                                                                    ". ",
                                                                    question
                                                                ]
                                                            }, index, true, {
                                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                                lineNumber: 417,
                                                                columnNumber: 27
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                        lineNumber: 415,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                                lineNumber: 413,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 375,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                lineNumber: 336,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>{
                                            setResult(null);
                                            setInput('');
                                        },
                                        className: "flex-1 bg-white text-gray-700 py-2 px-4 rounded-md font-medium border border-gray-300 hover:bg-gray-50 transition-colors",
                                        children: "Nova Consulta"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 428,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        className: "flex-1 bg-green-600 text-white py-2 px-4 rounded-md font-medium hover:bg-green-700 transition-colors",
                                        children: "💾 Salvar em Campanha"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                        lineNumber: 437,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                                lineNumber: 427,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                        lineNumber: 296,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/agentes/page.tsx",
                lineNumber: 202,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/agentes/page.tsx",
            lineNumber: 201,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/agentes/page.tsx",
        lineNumber: 200,
        columnNumber: 5
    }, this);
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__151bb413._.js.map