{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { <PERSON>alog, DialogPanel, DialogTitle } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  defaultMode?: 'login' | 'signup'\n}\n\nexport default function AuthModal({ isOpen, onClose, defaultMode = 'login' }: AuthModalProps) {\n  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp, user } = useAuth()\n  const router = useRouter()\n\n  // Redirect to dashboard when user logs in\n  useEffect(() => {\n    if (user && isOpen) {\n      console.log('User logged in, redirecting to dashboard...')\n      onClose()\n      router.push('/dashboard')\n    }\n  }, [user, isOpen, onClose, router])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      let result\n      if (mode === 'login') {\n        result = await signIn(email, password)\n      } else {\n        result = await signUp(email, password, fullName)\n      }\n\n      if (result.error) {\n        setError(result.error.message || 'Ocorreu um erro')\n      } else {\n        // Success - reset form\n        setEmail('')\n        setPassword('')\n        setFullName('')\n\n        if (mode === 'login') {\n          // For login, the useEffect will handle the redirect\n          console.log('Login successful, waiting for user state update...')\n        } else {\n          // For signup, show success message\n          setError('')\n          onClose()\n        }\n      }\n    } catch (err) {\n      setError('Ocorreu um erro inesperado')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetForm = () => {\n    setEmail('')\n    setPassword('')\n    setFullName('')\n    setError('')\n  }\n\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'signup' : 'login')\n    resetForm()\n  }\n\n  return (\n    <Dialog open={isOpen} onClose={onClose} className=\"relative z-50\">\n      <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\n\n      <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n        <DialogPanel className=\"mx-auto max-w-md w-full bg-white rounded-lg shadow-xl\">\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <DialogTitle className=\"text-lg font-semibold text-gray-900\">\n              {mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </DialogTitle>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n                {error}\n              </div>\n            )}\n\n            {mode === 'signup' && (\n              <div>\n                <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Nome Completo\n                </label>\n                <input\n                  id=\"fullName\"\n                  type=\"text\"\n                  value={fullName}\n                  onChange={(e) => setFullName(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"Seu nome completo\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                E-mail\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Senha\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                minLength={6}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"Mínimo 6 caracteres\"\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className={`w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium transition-colors ${loading\n                ? \"opacity-50 cursor-not-allowed\"\n                : \"hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                }`}\n            >\n              {loading ? 'Carregando...' : mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </button>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={switchMode}\n                className=\"text-sm text-blue-600 hover:text-blue-700 transition-colors\"\n              >\n                {mode === 'login'\n                  ? 'Não tem conta? Criar uma agora'\n                  : 'Já tem conta? Fazer login'\n                }\n              </button>\n            </div>\n          </form>\n        </DialogPanel>\n      </div>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAce,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,OAAO,EAAkB;IAC1F,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAqB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,0IAAO;IACxC,MAAM,SAAS,IAAA,+IAAS;IAExB,0CAA0C;IAC1C,IAAA,kNAAS,EAAC;QACR,IAAI,QAAQ,QAAQ;YAClB,QAAQ,GAAG,CAAC;YACZ;YACA,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAQ;QAAS;KAAO;IAElC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI;YACJ,IAAI,SAAS,SAAS;gBACpB,SAAS,MAAM,OAAO,OAAO;YAC/B,OAAO;gBACL,SAAS,MAAM,OAAO,OAAO,UAAU;YACzC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK,CAAC,OAAO,IAAI;YACnC,OAAO;gBACL,uBAAuB;gBACvB,SAAS;gBACT,YAAY;gBACZ,YAAY;gBAEZ,IAAI,SAAS,SAAS;oBACpB,oDAAoD;oBACpD,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,mCAAmC;oBACnC,SAAS;oBACT;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,QAAQ,SAAS,UAAU,WAAW;QACtC;IACF;IAEA,qBACE,8OAAC,yLAAM;QAAC,MAAM;QAAQ,SAAS;QAAS,WAAU;;0BAChD,8OAAC;gBAAI,WAAU;gBAA4B,eAAY;;;;;;0BAEvD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8LAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8LAAW;oCAAC,WAAU;8CACpB,SAAS,UAAU,WAAW;;;;;;8CAEjC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,8NAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIzB,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIJ,SAAS,0BACR,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAW;4CACX,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,iFAAiF,EAAE,UAC3F,kCACA,6FACA;8CAEH,UAAU,kBAAkB,SAAS,UAAU,WAAW;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,SAAS,UACN,mCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\nimport AuthModal from '@/components/auth/AuthModal'\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const [authModalOpen, setAuthModalOpen] = useState(false)\n  const { user, signOut } = useAuth()\n\n  const navigation = [\n    { name: 'Como funciona', href: '#como-funciona' },\n    { name: 'Agent<PERSON>', href: '#agentes' },\n    { name: 'Preço', href: '#preco' },\n  ]\n\n  const handleAuthClick = () => {\n    if (user) {\n      // Redirect to dashboard\n      window.location.href = '/dashboard'\n    } else {\n      setAuthModalOpen(true)\n    }\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <>\n      <header className=\"bg-white shadow-sm sticky top-0 z-40\">\n        <nav className=\"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8\" aria-label=\"Global\">\n          <div className=\"flex lg:flex-1\">\n            <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n              <span className=\"text-2xl font-bold text-primary-600\">AgentePub</span>\n            </Link>\n          </div>\n          \n          <div className=\"flex lg:hidden\">\n            <button\n              type=\"button\"\n              className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n              onClick={() => setMobileMenuOpen(true)}\n            >\n              <span className=\"sr-only\">Abrir menu principal</span>\n              <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n            </button>\n          </div>\n          \n          <div className=\"hidden lg:flex lg:gap-x-12\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"text-sm font-semibold leading-6 text-gray-900 hover:text-primary-600 transition-colors\"\n              >\n                {item.name}\n              </a>\n            ))}\n          </div>\n          \n          <div className=\"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\">\n            {user ? (\n              <div className=\"flex items-center gap-x-4\">\n                <span className=\"text-sm text-gray-700\">Olá, {user.email}</span>\n                <button\n                  onClick={handleAuthClick}\n                  className=\"text-sm font-semibold leading-6 text-primary-600 hover:text-primary-700 transition-colors\"\n                >\n                  Dashboard\n                </button>\n                <button\n                  onClick={handleSignOut}\n                  className=\"text-sm font-semibold leading-6 text-gray-600 hover:text-gray-700 transition-colors\"\n                >\n                  Sair\n                </button>\n              </div>\n            ) : (\n              <button\n                onClick={handleAuthClick}\n                className=\"text-sm font-semibold leading-6 text-gray-900 hover:text-primary-600 transition-colors\"\n              >\n                Entrar <span aria-hidden=\"true\">&rarr;</span>\n              </button>\n            )}\n          </div>\n        </nav>\n        \n        {/* Mobile menu */}\n        {mobileMenuOpen && (\n          <div className=\"lg:hidden\" role=\"dialog\" aria-modal=\"true\">\n            <div className=\"fixed inset-0 z-50\"></div>\n            <div className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"text-2xl font-bold text-primary-600\">AgentePub</span>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Fechar menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <a\n                        key={item.name}\n                        href={item.href}\n                        className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </a>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    {user ? (\n                      <div className=\"space-y-2\">\n                        <div className=\"text-sm text-gray-700 px-3\">Olá, {user.email}</div>\n                        <button\n                          onClick={() => {\n                            handleAuthClick()\n                            setMobileMenuOpen(false)\n                          }}\n                          className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-primary-600 hover:bg-gray-50 w-full text-left\"\n                        >\n                          Dashboard\n                        </button>\n                        <button\n                          onClick={() => {\n                            handleSignOut()\n                            setMobileMenuOpen(false)\n                          }}\n                          className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-600 hover:bg-gray-50 w-full text-left\"\n                        >\n                          Sair\n                        </button>\n                      </div>\n                    ) : (\n                      <button\n                        onClick={() => {\n                          handleAuthClick()\n                          setMobileMenuOpen(false)\n                        }}\n                        className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50 w-full text-left\"\n                      >\n                        Entrar\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      <AuthModal \n        isOpen={authModalOpen} \n        onClose={() => setAuthModalOpen(false)} \n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IAEjC,MAAM,aAAa;QACjB;YAAE,MAAM;YAAiB,MAAM;QAAiB;QAChD;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,MAAM,kBAAkB;QACtB,IAAI,MAAM;YACR,wBAAwB;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;wBAAkE,cAAW;;0CAC1F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uKAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;;;;;0CAI1D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC,8NAAS;4CAAC,WAAU;4CAAU,eAAY;;;;;;;;;;;;;;;;;0CAI/C,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;0CASpB,8OAAC;gCAAI,WAAU;0CACZ,qBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAwB;gDAAM,KAAK,KAAK;;;;;;;sDACxD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;yDAKH,8OAAC;oCACC,SAAS;oCACT,WAAU;;wCACX;sDACQ,8OAAC;4CAAK,eAAY;sDAAO;;;;;;;;;;;;;;;;;;;;;;;oBAOvC,gCACC,8OAAC;wBAAI,WAAU;wBAAY,MAAK;wBAAS,cAAW;;0CAClD,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,uKAAI;gDAAC,MAAK;gDAAI,WAAU;0DACvB,cAAA,8OAAC;oDAAK,WAAU;8DAAsC;;;;;;;;;;;0DAExD,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC,8NAAS;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;;;;;;;;kDAG/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;4DAEC,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS,IAAM,kBAAkB;sEAEhC,KAAK,IAAI;2DALL,KAAK,IAAI;;;;;;;;;;8DASpB,8OAAC;oDAAI,WAAU;8DACZ,qBACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEAA6B;oEAAM,KAAK,KAAK;;;;;;;0EAC5D,8OAAC;gEACC,SAAS;oEACP;oEACA,kBAAkB;gEACpB;gEACA,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEACC,SAAS;oEACP;oEACA,kBAAkB;gEACpB;gEACA,WAAU;0EACX;;;;;;;;;;;6EAKH,8OAAC;wDACC,SAAS;4DACP;4DACA,kBAAkB;wDACpB;wDACA,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjB,8OAAC,kJAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;;;AAIxC", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/teste/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport AuthModal from '@/components/auth/AuthModal'\nimport Navbar from '@/components/layout/Navbar'\nimport { \n  DocumentTextIcon, \n  SparklesIcon, \n  ArrowRightIcon,\n  CheckCircleIcon \n} from '@heroicons/react/24/outline'\n\nexport default function TestePage() {\n  const [briefing, setBriefing] = useState('')\n  const [isProcessing, setIsProcessing] = useState(false)\n  const [result, setResult] = useState<string | null>(null)\n  const [authModalOpen, setAuthModalOpen] = useState(false)\n  const { user } = useAuth()\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!user) {\n      setAuthModalOpen(true)\n      return\n    }\n\n    if (!briefing.trim()) return\n\n    setIsProcessing(true)\n    \n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 2000))\n    \n    // Generate simulated result based on briefing\n    const simulatedResult = generateSimulatedResult(briefing)\n    setResult(simulatedResult)\n    setIsProcessing(false)\n  }\n\n  const generateSimulatedResult = (briefingText: string) => {\n    const hasClient = briefingText.toLowerCase().includes('cliente')\n    const hasBudget = briefingText.toLowerCase().includes('budget') || briefingText.toLowerCase().includes('orçamento')\n    const hasTarget = briefingText.toLowerCase().includes('público') || briefingText.toLowerCase().includes('target')\n    const hasChannels = briefingText.toLowerCase().includes('canal') || briefingText.toLowerCase().includes('instagram') || briefingText.toLowerCase().includes('facebook')\n\n    return `\n## 📋 Briefing Estruturado\n\n**Cliente:** ${hasClient ? 'Identificado no briefing' : 'A definir'}\n**Objetivo:** Campanha de lançamento/divulgação\n**Prazo:** 30 dias (sugerido)\n\n## 🎯 Público-Alvo\n${hasTarget ? '• Público identificado no briefing\\n• Segmentação detalhada necessária' : '• Definir personas principais\\n• Idade: 25-45 anos\\n• Classe B/C'}\n\n## 💰 Budget\n${hasBudget ? '• Orçamento mencionado no briefing\\n• Distribuição por canal recomendada' : '• Definir orçamento total\\n• Sugestão: 70% mídia, 30% produção'}\n\n## 📱 Canais Recomendados\n${hasChannels ? '• Canais mencionados no briefing\\n• Otimização por performance' : '• Instagram (40%)\\n• Facebook (30%)\\n• Google Ads (30%)'}\n\n## 📅 Cronograma Sugerido\n**Semana 1:** Planejamento e aprovações\n**Semana 2:** Produção de materiais\n**Semana 3:** Lançamento da campanha\n**Semana 4:** Otimização e relatórios\n\n## 📊 KPIs Principais\n• Alcance: 100k+ pessoas\n• Engajamento: 3%+\n• CTR: 1.5%+\n• Conversões: A definir\n\n## ✅ Próximos Passos\n1. Validar informações com cliente\n2. Definir personas detalhadas\n3. Criar cronograma executivo\n4. Produzir materiais criativos\n    `\n  }\n\n  const handleSaveToCampaign = () => {\n    if (user) {\n      router.push('/dashboard?newCampaign=true')\n    } else {\n      setAuthModalOpen(true)\n    }\n  }\n\n  return (\n    <>\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        \n        <div className=\"max-w-4xl mx-auto px-6 py-12\">\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Teste o AgentePub gratuitamente\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              Cole um briefing e veja como nossos agentes transformam ele em material estruturado\n            </p>\n          </div>\n\n          {!result ? (\n            <div className=\"bg-white rounded-lg shadow-lg p-8\">\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"briefing\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Cole seu briefing aqui\n                  </label>\n                  <textarea\n                    id=\"briefing\"\n                    value={briefing}\n                    onChange={(e) => setBriefing(e.target.value)}\n                    rows={8}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none\"\n                    placeholder=\"Exemplo: Precisamos de uma campanha para o lançamento do novo produto X. Público: jovens 18-25 anos. Budget: R$ 50k. Prazo: 30 dias. Canais: Instagram, TikTok e Google Ads.\"\n                    required\n                  />\n                </div>\n\n                <button\n                  type=\"submit\"\n                  disabled={isProcessing || !briefing.trim()}\n                  className=\"w-full bg-primary-600 text-white py-3 px-6 rounded-md font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center gap-2\"\n                >\n                  {isProcessing ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                      Processando briefing...\n                    </>\n                  ) : (\n                    <>\n                      <SparklesIcon className=\"h-5 w-5\" />\n                      Processar com IA\n                    </>\n                  )}\n                </button>\n              </form>\n\n              {/* Example briefings */}\n              <div className=\"mt-8 pt-8 border-t border-gray-200\">\n                <h3 className=\"text-sm font-medium text-gray-900 mb-4\">💡 Exemplos de briefing:</h3>\n                <div className=\"space-y-2\">\n                  <button\n                    onClick={() => setBriefing(\"Campanha de Black Friday para e-commerce de moda. Público: mulheres 25-40 anos, classe B. Budget: R$ 80k. Canais: Instagram, Facebook e Google Ads. Prazo: 15 dias.\")}\n                    className=\"text-left text-sm text-primary-600 hover:text-primary-700 block\"\n                  >\n                    • Campanha Black Friday para e-commerce de moda\n                  </button>\n                  <button\n                    onClick={() => setBriefing(\"Lançamento de aplicativo de delivery. Target: jovens 18-30 anos em São Paulo. Budget: R$ 120k. Foco em Instagram, TikTok e influenciadores. Prazo: 45 dias.\")}\n                    className=\"text-left text-sm text-primary-600 hover:text-primary-700 block\"\n                  >\n                    • Lançamento de app de delivery\n                  </button>\n                  <button\n                    onClick={() => setBriefing(\"Campanha institucional para construtora. Público: famílias classe A/B, 30-50 anos. Budget: R$ 200k. Canais: TV, digital e outdoor. Prazo: 60 dias.\")}\n                    className=\"text-left text-sm text-primary-600 hover:text-primary-700 block\"\n                  >\n                    • Campanha institucional para construtora\n                  </button>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"space-y-6\">\n              {/* Result */}\n              <div className=\"bg-white rounded-lg shadow-lg p-8\">\n                <div className=\"flex items-center gap-2 mb-6\">\n                  <CheckCircleIcon className=\"h-6 w-6 text-green-500\" />\n                  <h2 className=\"text-xl font-semibold text-gray-900\">Briefing processado com sucesso!</h2>\n                </div>\n                \n                <div className=\"prose max-w-none\">\n                  <pre className=\"whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed\">\n                    {result}\n                  </pre>\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"bg-primary-50 rounded-lg p-6 text-center\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  Gostou do resultado?\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Salve este briefing em uma campanha e acesse todos os agentes inteligentes\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <button\n                    onClick={handleSaveToCampaign}\n                    className=\"bg-primary-600 text-white py-2 px-6 rounded-md font-medium hover:bg-primary-700 transition-colors flex items-center justify-center gap-2\"\n                  >\n                    Salvar em Campanha\n                    <ArrowRightIcon className=\"h-4 w-4\" />\n                  </button>\n                  <button\n                    onClick={() => {\n                      setResult(null)\n                      setBriefing('')\n                    }}\n                    className=\"bg-white text-gray-700 py-2 px-6 rounded-md font-medium border border-gray-300 hover:bg-gray-50 transition-colors\"\n                  >\n                    Testar Outro Briefing\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      <AuthModal \n        isOpen={authModalOpen} \n        onClose={() => setAuthModalOpen(false)} \n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAgB;IACpD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,0IAAO;IACxB,MAAM,SAAS,IAAA,+IAAS;IAExB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM;YACT,iBAAiB;YACjB;QACF;QAEA,IAAI,CAAC,SAAS,IAAI,IAAI;QAEtB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,8CAA8C;QAC9C,MAAM,kBAAkB,wBAAwB;QAChD,UAAU;QACV,gBAAgB;IAClB;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,YAAY,aAAa,WAAW,GAAG,QAAQ,CAAC;QACtD,MAAM,YAAY,aAAa,WAAW,GAAG,QAAQ,CAAC,aAAa,aAAa,WAAW,GAAG,QAAQ,CAAC;QACvG,MAAM,YAAY,aAAa,WAAW,GAAG,QAAQ,CAAC,cAAc,aAAa,WAAW,GAAG,QAAQ,CAAC;QACxG,MAAM,cAAc,aAAa,WAAW,GAAG,QAAQ,CAAC,YAAY,aAAa,WAAW,GAAG,QAAQ,CAAC,gBAAgB,aAAa,WAAW,GAAG,QAAQ,CAAC;QAE5J,OAAO,CAAC;;;aAGC,EAAE,YAAY,6BAA6B,YAAY;;;;;AAKpE,EAAE,YAAY,2EAA2E,mEAAmE;;;AAG5J,EAAE,YAAY,6EAA6E,iEAAiE;;;AAG5J,EAAE,cAAc,mEAAmE,0DAA0D;;;;;;;;;;;;;;;;;;;IAmBzI,CAAC;IACH;IAEA,MAAM,uBAAuB;QAC3B,IAAI,MAAM;YACR,OAAO,IAAI,CAAC;QACd,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iJAAM;;;;;kCAEP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;4BAKtC,CAAC,uBACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAA+C;;;;;;kEAGnF,8OAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,MAAM;wDACN,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDACC,MAAK;gDACL,UAAU,gBAAgB,CAAC,SAAS,IAAI;gDACxC,WAAU;0DAET,6BACC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;wDAAkE;;iFAInF;;sEACE,8OAAC,uOAAY;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;kDAQ5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,YAAY;wDAC3B,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,YAAY;wDAC3B,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,YAAY;wDAC3B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;qDAOP,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gPAAe;wDAAC,WAAU;;;;;;kEAC3B,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAGtD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;kDAMP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS;wDACT,WAAU;;4DACX;0EAEC,8OAAC,6OAAc;gEAAC,WAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDACC,SAAS;4DACP,UAAU;4DACV,YAAY;wDACd;wDACA,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUb,8OAAC,kJAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;;;AAIxC", "debugId": null}}]}